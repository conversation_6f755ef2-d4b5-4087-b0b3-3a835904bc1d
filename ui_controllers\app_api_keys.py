import uuid
import random
import string
from flask import Flask, request, jsonify, render_template, Blueprint
from auth_decorators import login_required
from Qdrant.api.utils.db import get_db_connection
from datetime import datetime, timedelta
import pandas as pd

# Create a Blueprint for API keys routes
api_keys_bp = Blueprint('api_keys', __name__, template_folder='templates')

def init_api_keys_routes(app: Flask):
    """Registers the API keys blueprint with the Flask app."""
    app.register_blueprint(api_keys_bp)
    return app

@api_keys_bp.route('/api_keys')
@login_required
def api_keys_page():
    """Renders the API keys management page."""
    return render_template('api_keys.html')

@api_keys_bp.route('/api/api_keys', methods=['GET'])
@login_required
def get_api_keys():
    """API endpoint to get all API keys and their usage stats."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get current and previous month start dates
        today = datetime.today()
        current_month_start = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        previous_month_end = current_month_start - timedelta(days=1)
        previous_month_start = previous_month_end.replace(day=1)

        # Fetch all API keys
        cursor.execute("SELECT api_key, client_id, client_name, rate_limit, daily_limit, created_at FROM public.check_client_api_keys ORDER BY created_at DESC")
        keys = cursor.fetchall()
        
        results = []
        for key in keys:
            api_key, client_id, client_name, rate_limit, daily_limit, created_at = key

            # Get total historical usage
            cursor.execute("SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s", (api_key,))
            total_usage = cursor.fetchone()[0]

            # Get current month usage
            cursor.execute(
                "SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s AND create_time >= %s",
                (api_key, current_month_start)
            )
            current_month_usage = cursor.fetchone()[0]

            # Get previous month usage
            cursor.execute(
                "SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s AND create_time >= %s AND create_time < %s",
                (api_key, previous_month_start, current_month_start)
            )
            previous_month_usage = cursor.fetchone()[0]

            results.append({
                'api_key': api_key,
                'client_id': client_id,
                'client_name': client_name,
                'rate_limit': rate_limit,
                'daily_limit': daily_limit,
                'created_at': created_at.isoformat(),
                'total_usage': total_usage,
                'current_month_usage': current_month_usage,
                'previous_month_usage': previous_month_usage
            })

        cursor.close()
        return jsonify(results)

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


def generate_random_string(length):
    """Generates a random string of digits of a given length."""
    return ''.join(random.choices(string.digits, k=length))

def generate_api_key(client_type):
    """Generates an API key based on the client type."""
    while True:
        if client_type == 'Internal':
            key_part1 = '37'
            key_part2 = generate_random_string(3)
            key_part3 = generate_random_string(5)
            key_part4 = generate_random_string(4)
            key_part5 = generate_random_string(4)
            new_key = f"{key_part1}{key_part2}-{key_part3}-{key_part4}-{key_part5}"
        elif client_type == 'IP Center':
            new_key = "89789-47484-1971" + generate_random_string(4)
        else:
            part1 = generate_random_string(5)
            part2 = generate_random_string(5)
            part3 = generate_random_string(4)
            part4 = generate_random_string(4)
            new_key = f"{part1}-{part2}-{part3}-{part4}"

        # Check if the key already exists
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM public.check_client_api_keys WHERE api_key = %s", (new_key,))
        if cursor.fetchone()[0] == 0:
            cursor.close()
            conn.close()
            return new_key
        cursor.close()
        conn.close()


def get_next_client_id(client_type):
    """Gets the next available client ID based on the client type."""
    conn = get_db_connection()
    cursor = conn.cursor()

    if client_type == 'Internal':
        prefix = '1'
        query = "SELECT MAX(CAST(client_id AS INTEGER)) FROM public.check_client_api_keys WHERE CAST(client_id AS TEXT) LIKE '1___'"
    elif client_type == 'Large Seller':
        prefix = '2'
        query = "SELECT MAX(CAST(client_id AS INTEGER)) FROM public.check_client_api_keys WHERE CAST(client_id AS TEXT) LIKE '2___'"
    elif client_type == 'IP Center':
        prefix = '30'
        query = "SELECT MAX(CAST(client_id AS INTEGER)) FROM public.check_client_api_keys WHERE CAST(client_id AS TEXT) LIKE '30__'"
    elif client_type == 'Partner':
        prefix = '40'
        query = "SELECT MAX(CAST(client_id AS INTEGER)) FROM public.check_client_api_keys WHERE CAST(client_id AS TEXT) LIKE '40__'"
    else:
        return None

    cursor.execute(query)
    max_id = cursor.fetchone()[0]
    cursor.close()
    conn.close()

    if max_id is None:
        if client_type in ['Internal', 'Large Seller']:
            return int(prefix + '000')
        else:
            return int(prefix + '00')
    else:
        return max_id + 1


@api_keys_bp.route('/api/api_keys', methods=['POST'])
@login_required
def add_api_key():
    """API endpoint to create a new API key."""
    data = request.get_json()
    client_type = data.get('client_type')
    client_name = data.get('client_name')
    rate_limit = data.get('rate_limit', 1)
    daily_limit = data.get('daily_limit', 100)

    if not client_type or not client_name:
        return jsonify({'error': 'Client Type and Client Name are required'}), 400

    client_id = get_next_client_id(client_type)
    if client_id is None:
        return jsonify({'error': 'Invalid Client Type'}), 400

    new_api_key = generate_api_key(client_type)
    
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO public.check_client_api_keys (api_key, client_id, client_name, rate_limit, daily_limit)
            VALUES (%s, %s, %s, %s, %s)
            """,
            (new_api_key, str(client_id), client_name, rate_limit, daily_limit)
        )
        conn.commit()
        cursor.close()
        return jsonify({'status': 'success', 'api_key': new_api_key, 'client_id': client_id})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()


@api_keys_bp.route('/api/api_keys/<string:api_key>', methods=['DELETE'])
@login_required
def delete_api_key(api_key):
    """API endpoint to delete an API key if it has no usage."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check for historical usage
        cursor.execute("SELECT COUNT(*) FROM public.check_api_requests WHERE api_key = %s", (api_key,))
        total_usage = cursor.fetchone()[0]

        if total_usage > 0:
            return jsonify({'error': 'Cannot delete API key with historical usage.'}), 400

        # Delete the key
        cursor.execute("DELETE FROM public.check_client_api_keys WHERE api_key = %s", (api_key,))
        conn.commit()
        
        if cursor.rowcount == 0:
            return jsonify({'error': 'API key not found.'}), 404

        cursor.close()
        return jsonify({'status': 'success'})
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()

@api_keys_bp.route('/api/api_keys/refresh', methods=['POST'])
@login_required
def refresh_keys_on_servers():
    """
    API endpoint to trigger a refresh of API keys on both the main and backup servers.
    """
    import os
    import requests

    bearer_token = os.getenv("API_BEARER_TOKEN")
    if not bearer_token:
        return jsonify({'error': 'API_BEARER_TOKEN is not configured on the server.'}), 500

    headers = {
        'Authorization': f'Bearer {bearer_token}',
        'Content-Type': 'application/json'
    }
    
    servers = {
        "main": "https://apiv.maidalv.com/admin/refresh-api-keys",
        "backup": "https://apig.maidalv.com/admin/refresh-api-keys"
    }
    
    results = {}
    
    for server_name, url in servers.items():
        try:
            response = requests.post(url, headers=headers, timeout=15)
            if response.status_code == 200:
                results[server_name] = {'status': 'success', 'message': response.json().get('message', 'OK')}
            else:
                error_message = response.json().get('message', response.text)
                results[server_name] = {'status': 'error', 'message': f"Failed with status {response.status_code}: {error_message}"}
        except requests.exceptions.RequestException as e:
            results[server_name] = {'status': 'error', 'message': f"Request failed: {str(e)}"}

    return jsonify(results)