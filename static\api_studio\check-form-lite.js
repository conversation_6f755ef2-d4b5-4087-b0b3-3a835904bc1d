// Check form submission logic

// Initialize check form functionality
function initializeCheckForm() {
    const checkForm = document.getElementById("checkForm");
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const urlInput = document.getElementById("main_product_image_url");
    const fileInput = document.getElementById("main_product_image_upload");
    
    if (!checkForm) return;

    // Prevent submitting both file and URL for main product image
    checkForm.addEventListener("submit", function(e) {
        if (uploadActiveInput.value === "true" && urlInput.value.trim() !== "") {
            alert("Please either upload a file OR provide a URL, not both.");
            e.preventDefault();
            return;
        }
        if (uploadActiveInput.value === "false") {
            fileInput.value = "";
        }
    });

    // Main submission logic with spinner and temporary message
    checkForm.addEventListener("submit", function(e) {
        e.preventDefault();
        
        // Disable the submit button to prevent duplicate submissions
        const submitButton = this.querySelector("button[type='submit']");
        submitButton.disabled = true;
        
        // Display a spinner and processing message
        const outputDiv = document.getElementById("output");
        outputDiv.innerHTML = `
            <div class="info-message">
                <div style='display:flex; align-items:center; gap:10px;'>
                    <div class='spinner'></div>
                    <span data-i18n='processing'></span>
                </div>
            </div>`;
        
        // Trigger language update immediately after creating the message
        document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;

        // Immediate scroll to top with header offset
        const header = document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 0;
        
        // New approach: Use documentElement scroll with CSS scroll-padding
        document.documentElement.style.scrollPaddingTop = `${headerHeight}px`;
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        // Gather form values
        let formData;
        try {
            formData = gatherFormData();
        } catch (error) {
            handleError(error, outputDiv, submitButton);
            return;
        }

        // Add timeout controller
        const controller = new AbortController();
        const timeoutDuration = 600000; // 10 minutes
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        // Make the API call
        // const api_endpoint = "http://localhost:5000/check_api";
        // const api_endpoint = "http://vectorstore1.maidalv.com:5090/check_api";
        // const api_endpoint = "https://api.maidalv.com/check_api";
        const api_endpoint = "/check_lite";
        
        submitCheckRequest(api_endpoint, formData, controller, timeoutId, submitButton, outputDiv);
    });
}

// Gather form data
function gatherFormData() {
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const previewImage = document.getElementById("main_product_image_preview");
    const urlInput = document.getElementById("main_product_image_url");
    
    const api_key = document.getElementById("api_key").value;
    const mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
    
    const ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
    const description = document.getElementById("description").value;
    
    return {
        api_key: api_key,
        main_product_image: mainProductImageValue,
        ip_keywords: ip_keywords,
        description: description,
        language: document.getElementById("language").value
    };
}

// Submit check request
function submitCheckRequest(api_endpoint, data, controller, timeoutId, submitButton, outputDiv) {
    fetch(api_endpoint, {
        method: "POST",
        headers: {"Content-Type": "application/json"},
        body: JSON.stringify(data),
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        return handleResponse(response);
    })
    .then(response => {
        // Since the call is synchronous, we expect a 'completed' status directly.
        if (response.status === 'completed') {
            console.log("Received completed result directly.");
            // The 'displayResults' function is expected to be in another file (e.g., main.js)
            // Ensure it is loaded in the HTML.
            displayResults(response.result);
            submitButton.disabled = false;
        } else {
            // Handle cases where the server returns an error status
            const error = new Error(response.message || `Received unexpected status: ${response.status}`);
            error.error_code = response.error_code;
            error.details = response.details;
            throw error;
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        handleError(error, outputDiv, submitButton);
    });
}

// Handle response
function handleResponse(response) {
    if (response.ok) {
        if (!response.headers.get("content-type")?.includes("application/json")) {
            return Promise.reject(new Error(`Invalid content type: ${response.headers.get("content-type")}`));
        }
        return response.json();
    }

    // If response is not ok, get text first to avoid JSON parsing errors on non-JSON bodies.
    return response.text().then(errorText => {
        let errorJson;
        try {
            // Try to parse the error text as JSON.
            errorJson = JSON.parse(errorText);
        } catch (e) {
            // If it's not JSON, create a standard error structure.
            const error = new Error(errorText || `HTTP Error ${response.status}: ${response.statusText}`);
            error.error_code = 'NETWORK_ERROR';
            error.details = `The server returned a non-JSON error for a ${response.status} status.`;
            throw error;
        }

        // If it is JSON, extract the details.
        const errorData = errorJson.error || {};
        const message = errorData.message || `Error ${response.status}: ${response.statusText}`;
        const error = new Error(message);
        error.error_code = errorData.error_code;
        error.details = errorData.details || errorText; // Fallback to raw text if details are missing
        throw error;
    });
}

// Handle errors
function handleError(error, outputDiv, submitButton) {
    const language = document.getElementById("language").value;
    const i18nBase = i18n[language];

    // Sanitize error code to prevent issues with keys and create the main message key
    const errorCode = (error.error_code || 'network_error').toLowerCase().replace(/-/g, '_');
    const mainMessageKey = `error_${errorCode}`;
    let displayMessage = i18nBase[mainMessageKey] || error.message || i18nBase.error_network;

    // Handle special case for AbortError (fetch timeout)
    if (error.name === 'AbortError') {
        displayMessage = i18nBase.error_timeout;
        error.error_code = 'TIMEOUT_ERROR';
    }

    // Log details for debugging
    if (error.details) {
        console.error("Error Details:", error.details);
    }

    // Translate the details if a key exists
    let detailsHtml = '';
    if (error.details) {
        // The detail key is constructed from the `details` field.
        // This handles cases where the detail might be `invalid_url` or `base64_decoding_failed: ...`
        const detailCode = String(error.details).toLowerCase().split(':')[0].trim().replace(/-/g, '_');
        const detailKey = `detail_${detailCode}`;
        const translatedDetail = i18nBase[detailKey];
        
        if (translatedDetail) {
            detailsHtml = `<p class="error-details">${translatedDetail}</p>`;
        } else {
            // Fallback to raw details if no specific translation is found
            detailsHtml = `<p class="error-details">${error.details}</p>`;
        }
    }

    // Display the error with better formatting
    outputDiv.innerHTML = `
        <div class="error-message">
            <strong>Error</strong>
            <p>${displayMessage}</p>
            ${detailsHtml}
            ${error.error_code ? `<div class="error-code">${error.error_code}</div>` : ''}
        </div>
    `;

    // Re-enable submit button even on error
    if (submitButton) {
        submitButton.disabled = false;
    }
}

// Export functions for global access
window.initializeCheckForm = initializeCheckForm;
