import os
from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async


def get_report_prompt(ip_type, result, language='zh'):
    """Get appropriate report prompt based on IP type and parameters"""
    prompts_dir = os.path.join(os.getcwd(), "Check", "Prompts")
    
    if ip_type == "Patent":
        if "D" in result.get('reg_no', ''):
            base_prompt_file = "Report_Patent_Design"
        else:
            base_prompt_file = "Report_Patent_Utility"
    elif ip_type == "Copyright":
        base_prompt_file = "Report_Copyright"
    elif ip_type == "Trademark":
        if "ip_local_paths" in result and len(result["ip_local_paths"]) > 0:
            base_prompt_file = "Report_Trademark_Images"
        else:
            base_prompt_file = "Report_Trademark_Text"
    else:
        raise ValueError(f"Unsupported IP type: {ip_type}")

    prompt_file = f"{base_prompt_file}{'_CN' if language == 'zh' else ''}.txt"
    
    with open(os.path.join(prompts_dir, prompt_file), "r", encoding="utf-8") as f:
        return f.read()


def get_report_config(ip_type):
    """Get report configuration based on IP type"""
    configs = {
        "Patent": {
            "title": "**Patent Risk Assessment Report**",
            "start_marker": "**1. Patent Information:**",
            "end_marker": "**End of Report**"
        },
        "Copyright": {
            "title": "**Copyright Risk Assessment Report**",
            "start_marker": "**1. Image Information:**",
            "end_marker": "**End of Report**"
        },
        "Trademark": {
            "title": "**Trademark Risk Assessment Report**",
            "start_marker": "**1. Mark Information:**",
            "end_marker": "**End of Report**"
        }
    }
    return configs.get(ip_type, configs["Copyright"])


def _get_risk_assessment(score, is_tro, ip_type, language='zh'):
    """
    Determines the risk level and description based on the score, TRO status, and IP type.
    """
    if language == 'zh':
        ip_type_map = { "Patent": "专利", "Copyright": "版权", "Trademark": "商标" }
        ip_type_lang = ip_type_map.get(ip_type, "知识产权")
        if is_tro:
            if score > 5: level, description = "高风险", f"与已知TRO原告的{ip_type_lang}高度相似。存在很高的诉讼风险。"
            elif score > 2: level, description = "中风险", f"与已知TRO原告的{ip_type_lang}有中等程度的相似性。存在中等的诉讼风险。"
            else: level, description = "低风险", f"与已知TRO原告的{ip_type_lang}相似度较低，但由于原告的诉讼历史，风险仍然存在。"
        else:
            if score > 6: level, description = "高风险", f"与已注册的{ip_type_lang}高度相似。存在很大的侵权投诉风险。"
            elif score > 3: level, description = "中风险", f"与已注册的{ip_type_lang}有中等程度的相似性。建议谨慎行事。"
            else: level, description = "低风险", f"与已注册的{ip_type_lang}相似度较低。投诉风险被认为很低。"
    else: # English
        ip_type_lang = ip_type
        if is_tro:
            if score > 5: level, description = "High Risk", f"High similarity to IP of a known TRO plaintiff. There is a high risk of litigation."
            elif score > 2: level, description = "Medium Risk", f"Moderate similarity to IP of a known TRO plaintiff. There is a medium risk of litigation."
            else: level, description = "Low Risk", f"Low similarity to IP of a known TRO plaintiff, but risk still exists due to the plaintiff's litigation history."
        else:
            if score > 6: level, description = "High Risk", f"High similarity to a registered {ip_type_lang}. There is a significant risk of an infringement complaint."
            elif score > 3: level, description = "Medium Risk", f"Moderate similarity to a registered {ip_type_lang}. Caution is advised."
            else: level, description = "Low Risk", f"Low similarity to a registered {ip_type_lang}. The risk of a complaint is considered low."

    return level, description


async def create_check_report(ip_type, check_id, result, client=None, bucket=None, model_name="gemini-2.0-flash-exp", description=None, keywords=None, reference_text=None, language='zh'):
    """
    Centralized function to create IP infringement reports
    
    Args:
        ip_type: Type of IP ("Patent", "Copyright", "Trademark")
        check_id: Check ID for URL generation
        result: Result dictionary containing IP information
        client: COS client for file operations
        bucket: COS bucket name
        model_name: Model name for AI generation
    
    Returns:
        Updated result dictionary with report, or None if no report needed
    """
    # Validate required data
    # if 'download_statuses' not in result or not all(result['download_statuses']):
    #     return None
    
    if not os.path.exists(result['product_local_path']) or any(not os.path.exists(ip_local_path) for ip_local_path in result['ip_local_paths']):
        return None
    
    # Get report configuration
    config = get_report_config(ip_type)
    report_prompt = get_report_prompt(ip_type, result, language)
    
    # Build prompt list based on IP type and query type
    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", "\n\nProduct Image:"),
        ("image_path", result["product_local_path"]),
    ]
    product_url = create_product_url(check_id, result['product_local_path'])
    image_url = ["", "", "", product_url.replace(" ", "%20").replace("http:", "https:")]
    
    # Add IP-specific content to prompt
    if ip_type == "Patent":
        registered_patent = "Registered Design Patent" if "D" in result['reg_no'] else "Registered Utility Patent"
        prompt_list.append(("text", f"\n\n{registered_patent} with registration number {result['reg_no']}:\n"))
        image_url.append("")
        
        # Add all patent images
        for ip_file_local, ip_url in zip(result['ip_local_paths'], result['ip_asset_urls']):
            prompt_list.append(("image_path", ip_file_local))
            image_url.append(ip_url.replace(" ", "%20").replace("http:", "https:"))

    elif ip_type == "Copyright":
        prompt_list.extend([
            ("text", f"\n\nCopyright Registered Image from '{result['ip_owner']}' with registration number '{result['reg_no']}':"),
            ("image_path", result['ip_local_paths'][0]),
        ])
        image_url.extend(["", result['ip_asset_urls'][0].replace(" ", "%20").replace("http:", "https:")])
        
    elif ip_type == "Trademark":
        if "ip_local_paths" in result and len(result["ip_local_paths"]) > 0:
            for ip_file_local, ip_url in zip(result['ip_local_paths'], result['ip_asset_urls']):
                reg_no_str = ', '.join(map(str, result['reg_no'])) if isinstance(result['reg_no'], list) else result['reg_no']
                int_cls_str = ', '.join(map(str, result.get('int_cls_list', ''))) if isinstance(result.get('int_cls_list'), list) else result.get('int_cls_list', '')
                prompt_list.append(("text", f"\n\nTrademark Registered Image with Registration Number {reg_no_str} and International Classification {int_cls_str}:"))
                prompt_list.append(("image_path", ip_file_local))
                image_url.extend(["", ip_url.replace(" ", "%20").replace("http:", "https:")])
        else:
            text_prompt = f"""
            **Product Information:**
            - Product Description: {description or "Not provided"}
            - Keywords: {keywords or "Not provided"}
            - Reference Text: {reference_text or "Not provided"}

            **Registered Trademarks to Analyze:**
            - Trademark Text:** {result['text']}
            - Owner:** {result['ip_owner']}
            - Registration Number:** {', '.join(map(str, result.get('reg_no', []))) if isinstance(result.get('reg_no'), list) else result.get('reg_no', 'Not available')}
            - Goods and Services:** {' | '.join(result.get('goods_services', [])) if isinstance(result.get('goods_services'), list) else result.get('goods_services', 'Not available')}
            """
            
            prompt_list.append(("text", text_prompt))
            image_url.append("")
    
        
    # Get AI response
    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url, model_name=model_name)
    risk_assessment = get_json(ai_answer)
    
    try:
        final_answer_score = int(risk_assessment["final_answer"])
    except:
        final_answer_score = 0  # Default to 0 for "Report not required"
    
    if final_answer_score > 0:
        # Extract and format report
        report = get_report(ai_answer, config["start_marker"], config["end_marker"])
        report = config["title"] + "\n\n" + report
        
        # Update result with report information
        is_tro = "plaintiff_id" in result and result["plaintiff_id"]
        risk_score = final_answer_score
        if is_tro:
            risk_score = min(risk_score + 1, 10)
        risk_level, risk_description = _get_risk_assessment(final_answer_score, is_tro, ip_type, language)
                
        result["report"] = report
        result["risk_level"] = risk_level
        result["risk_score"] = risk_score
        result["risk_description"] = risk_description
        result["product_url"] = product_url
        
        return result
    else:
        return None


def create_product_url(check_id, product_image_path):
    """Create product URL for COS storage"""
    product_url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(product_image_path)}"
    return product_url