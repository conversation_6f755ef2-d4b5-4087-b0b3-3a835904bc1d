// Check form submission logic

// Initialize check form functionality
function initializeCheckForm() {
    const checkForm = document.getElementById("checkForm");
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const urlInput = document.getElementById("main_product_image_url");
    const fileInput = document.getElementById("main_product_image_upload");
    
    if (!checkForm) return;

    // Prevent submitting both file and URL for main product image
    checkForm.addEventListener("submit", function(e) {
        if (uploadActiveInput.value === "true" && urlInput.value.trim() !== "") {
            alert("Please either upload a file OR provide a URL, not both.");
            e.preventDefault();
            return;
        }
        if (uploadActiveInput.value === "false") {
            fileInput.value = "";
        }
    });

    // Additional validation for form submission
    checkForm.addEventListener("submit", function(e) {
        const otherUploadActiveInput = document.getElementById("other_product_images_upload_active");
        const otherUrlInput = document.getElementById("other_product_images_url");
        const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
        const ipUrlInput = document.getElementById("ip_images_url");
        const otherFilesInput = document.getElementById("other_product_images_upload");
        const ipFilesInput = document.getElementById("ip_images_upload");

        const otherUploadActive = otherUploadActiveInput.value;
        const otherUrl = otherUrlInput.value.trim();
        if(otherUploadActive === "true" && otherUrl !== "") {
            alert("Please either upload Other Product Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        const ipUploadActive = ipUploadActiveInput.value;
        const ipUrl = ipUrlInput.value.trim();
        if(ipUploadActive === "true" && ipUrl !== "") {
            alert("Please either upload IP Images OR provide URLs, not both.");
            e.preventDefault();
            return;
        }
        if(otherUploadActive === "false") {
            otherFilesInput.value = "";
        }
        if(ipUploadActive === "false") {
            ipFilesInput.value = "";
        }
    });

    // Main submission logic with spinner and temporary message
    checkForm.addEventListener("submit", function(e) {
        e.preventDefault();
        
        // Disable the submit button to prevent duplicate submissions
        const submitButton = this.querySelector("button[type='submit']");
        submitButton.disabled = true;
        
        // Display a spinner and processing message
        const outputDiv = document.getElementById("output");
        outputDiv.innerHTML = `
            <div class="info-message">
                <div style='display:flex; align-items:center; gap:10px;'>
                    <div class='spinner'></div>
                    <span data-i18n='processing'></span>
                </div>
            </div>`;
        
        // Trigger language update immediately after creating the message
        document.querySelector('[data-i18n="processing"]').textContent = i18n[document.getElementById("language").value].processing;

        // Immediate scroll to top with header offset
        const header = document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 0;
        
        // New approach: Use documentElement scroll with CSS scroll-padding
        document.documentElement.style.scrollPaddingTop = `${headerHeight}px`;
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        // Gather form values
        let formData;
        try {
            formData = gatherFormData();
        } catch (error) {
            handleError(error, outputDiv, submitButton);
            return;
        }

        // Add timeout controller
        const controller = new AbortController();
        const timeoutDuration = 600000; // 10 minutes
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        // Make the API call
        // const api_endpoint = "http://localhost:5000/check_api";
        // const api_endpoint = "http://vectorstore1.maidalv.com:5090/check_api";
        // const api_endpoint = "https://api.maidalv.com/check_api";
        const api_endpoint = "/check_api";
        
        submitCheckRequest(api_endpoint, formData, controller, timeoutId, submitButton, outputDiv);
    });
}

// Gather form data
function gatherFormData() {
    const uploadActiveInput = document.getElementById("main_product_image_upload_active");
    const previewImage = document.getElementById("main_product_image_preview");
    const urlInput = document.getElementById("main_product_image_url");
    
    const api_key = document.getElementById("api_key").value;
    const mainProductImageValue = (uploadActiveInput.value === "true") ? previewImage.src : urlInput.value.trim();
    
    // For other product images
    let other_product_images;
    if (document.getElementById("other_product_images_upload_active").value === "true") {
        const previewImages = document.querySelectorAll("#other_product_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("Other Product Images are set to upload, but no files were provided.");
        }
        other_product_images = Array.from(previewImages).map(img => img.src);
    } else {
        other_product_images = document.getElementById("other_product_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    }

    // For IP images, read directly from preview images if uploaded
    const ipUploadActiveInput = document.getElementById("ip_images_upload_active");
    let ip_images;
    if (ipUploadActiveInput.value === "true") {
        const previewImages = document.querySelectorAll("#ip_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("IP Images are set to upload, but no files were provided.");
        }
        ip_images = Array.from(previewImages).map(img => img.src);
    } else {
        ip_images = document.getElementById("ip_images_url").value.split(",").map(item => item.trim()).filter(item => item);
    }

    const ip_keywords = document.getElementById("ip_keywords").value.split(",").map(item => item.trim()).filter(item => item);
    const description = document.getElementById("description").value;
    const reference_text = document.getElementById("reference_text").value;

    // For Reference images, read directly from preview images if uploaded
    let reference_images;
    if (document.getElementById("reference_images_upload_active").value === "true") {
        const previewImages = document.querySelectorAll("#reference_images_previews img");
        if (previewImages.length === 0) {
            throw new Error("Reference Images are set to upload, but no files were provided.");
        }
        reference_images = Array.from(previewImages).map(img => img.src);
    } else {
        reference_images = document.getElementById("reference_images").value.split(",").map(item => item.trim()).filter(item => item);
    }
    
    return {
        api_key: api_key,
        main_product_image: mainProductImageValue,
        other_product_images: other_product_images,
        ip_images: ip_images,
        ip_keywords: ip_keywords,
        description: description,
        reference_text: reference_text,
        reference_images: reference_images,
        language: document.getElementById("language").value
    };
}

// Submit check request
function submitCheckRequest(api_endpoint, data, controller, timeoutId, submitButton, outputDiv) {
    fetch(api_endpoint, {
        method: "POST",
        headers: {"Content-Type": "application/json"},
        body: JSON.stringify(data),
        signal: controller.signal
    })
    .then(response => {
        clearTimeout(timeoutId);
        return handleResponse(response);
    })
    .then(initialResponse => {
        if (!initialResponse.check_id) throw new Error('Missing check ID in response');

        if (initialResponse.status === 'failed' || initialResponse.error) {
            const errorMessage = initialResponse.error || 'An unknown error occurred';
            throw new Error(errorMessage);
        }

        // Handle the new synchronous 'completed' status for debugging
        if (initialResponse.status === 'completed') {
            console.log("Received completed result directly, skipping polling.");
            displayResults(initialResponse.result);
            submitButton.disabled = false;
        } else if (initialResponse.status === 'queued' || initialResponse.status === 'processing') {
            // Handle the normal asynchronous flow
            updateProcessingStatusUI(outputDiv, initialResponse.status, initialResponse.estimated_completion_time);
            startPolling(initialResponse.check_id, outputDiv, submitButton);
        } else {
            // Handle any other unexpected status
            throw new Error(`Received unexpected status: ${initialResponse.status}`);
        }
    })
    .catch(error => {
        clearTimeout(timeoutId);
        handleError(error, outputDiv, submitButton);
    });
}

// Handle response
function handleResponse(response) {
    if (response.ok) {
        if (!response.headers.get("content-type")?.includes("application/json")) {
            return Promise.reject(new Error(`Invalid content type: ${response.headers.get("content-type")}`));
        }
        return response.json();
    }

    // If response is not ok, get text first to avoid JSON parsing errors on non-JSON bodies.
    return response.text().then(errorText => {
        let errorJson;
        try {
            // Try to parse the error text as JSON.
            errorJson = JSON.parse(errorText);
        } catch (e) {
            // If it's not JSON, create a standard error structure.
            const error = new Error(errorText || `HTTP Error ${response.status}: ${response.statusText}`);
            error.error_code = 'NETWORK_ERROR';
            error.details = `The server returned a non-JSON error for a ${response.status} status.`;
            throw error;
        }

        // If it is JSON, extract the details.
        const errorData = errorJson.error || {};
        const message = errorData.message || `Error ${response.status}: ${response.statusText}`;
        const error = new Error(message);
        error.error_code = errorData.error_code;
        error.details = errorData.details || errorText; // Fallback to raw text if details are missing
        throw error;
    });
}

// Start polling for results
function startPolling(checkId, outputDiv, submitButton) {
    const maxPolls = 120; // Increased max polls for longer potential queue times
    let polls = 0;

    // Use a recursive setTimeout to prevent overlapping requests
    const poll = () => {
        if (polls++ >= maxPolls) {
            const timeoutError = new Error('Analysis timed out');
            timeoutError.error_code = 'TIMEOUT_ERROR';
            handleError(timeoutError, outputDiv, submitButton);
            return;
        }

        fetch(`/check_status/${checkId}`)
            .then(handleResponse)
            .then(statusResponse => {
                console.log("Polling response:", statusResponse);

                if (statusResponse.status === 'error') {
                    const error = new Error(statusResponse.message || 'An unknown polling error occurred.');
                    error.error_code = statusResponse.error_code || 'POLLING_FAILED';
                    error.details = statusResponse.details;
                    throw error;
                }

                if (statusResponse.status === 'completed') {
                    displayResults(statusResponse.result);
                    submitButton.disabled = false;
                } else {
                    let pollInterval;
                    if (statusResponse.status === 'queued') {
                        pollInterval = 20000; // 20 seconds
                    } else if (statusResponse.status === 'processing') {
                        pollInterval = 3000; // 3 seconds
                    } else {
                        pollInterval = 5000; // Default fallback for other states
                    }
                    
                    // If still queued or processing, update UI and poll again after the interval
                    if (statusResponse.status === 'queued' || statusResponse.status === 'processing') {
                        updateProcessingStatusUI(outputDiv, statusResponse.status, statusResponse.estimated_completion_time);
                    }
                    setTimeout(poll, pollInterval);
                }
            })
            .catch(error => {
                // On any error, stop polling and display the error
                handleError(error, outputDiv, submitButton);
            });
    };

    // Start the first poll after a 15-second delay
    setTimeout(poll, 15000);
}

// New helper function to update the UI for processing/queued states
function updateProcessingStatusUI(outputDiv, status, estimatedTime) {
    const lang = document.getElementById("language").value;
    const i18nData = i18n[lang];
    
    // Determine the message key based on the status ('queued' or 'processing')
    const statusMessageKey = status === 'queued' ? 'queued' : 'processing';
    const statusMessage = i18nData[statusMessageKey] || status; // Fallback to the status itself

    // Format the estimated time message, converting seconds to a more readable format
    let estimatedTimeMessage = '';
    if (estimatedTime !== undefined) {
        const minutes = Math.floor(estimatedTime / 60);
        const seconds = estimatedTime % 60;
        let timeString = '';
        if (minutes > 0) {
            timeString += `${minutes} ${i18nData.minutes || 'min'} `;
        }
        if (seconds > 0 || minutes === 0) {
            timeString += `${seconds} ${i18nData.seconds || 'sec'}`;
        }
        estimatedTimeMessage = `${i18nData.estimated_time || 'Estimated time'}: ${timeString.trim()}`;
    }

    outputDiv.innerHTML = `
        <div class="info-message">
            <div style='display:flex; align-items:center; gap:10px;'>
                <div class='spinner'></div>
                <span>${statusMessage}</span>
                ${estimatedTimeMessage ? `<span>(${estimatedTimeMessage})</span>` : ''}
            </div>
        </div>`;
}

// Handle errors
function handleError(error, outputDiv, submitButton) {
    const language = document.getElementById("language").value;
    const i18nBase = i18n[language];

    // Sanitize error code to prevent issues with keys and create the main message key
    const errorCode = (error.error_code || 'network_error').toLowerCase().replace(/-/g, '_');
    const mainMessageKey = `error_${errorCode}`;
    let displayMessage = i18nBase[mainMessageKey] || error.message || i18nBase.error_network;

    // Handle special case for AbortError (fetch timeout)
    if (error.name === 'AbortError') {
        displayMessage = i18nBase.error_timeout;
        error.error_code = 'TIMEOUT_ERROR';
    }

    // Log details for debugging
    if (error.details) {
        console.error("Error Details:", error.details);
    }

    // Translate the details if a key exists
    let detailsHtml = '';
    if (error.details) {
        // The detail key is constructed from the `details` field.
        // This handles cases where the detail might be `invalid_url` or `base64_decoding_failed: ...`
        const detailCode = String(error.details).toLowerCase().split(':')[0].trim().replace(/-/g, '_');
        const detailKey = `detail_${detailCode}`;
        const translatedDetail = i18nBase[detailKey];
        
        if (translatedDetail) {
            detailsHtml = `<p class="error-details">${translatedDetail}</p>`;
        } else {
            // Fallback to raw details if no specific translation is found
            detailsHtml = `<p class="error-details">${error.details}</p>`;
        }
    }

    // Display the error with better formatting
    outputDiv.innerHTML = `
        <div class="error-message">
            <strong>Error</strong>
            <p>${displayMessage}</p>
            ${detailsHtml}
            ${error.error_code ? `<div class="error-code">${error.error_code}</div>` : ''}
        </div>
    `;

    // Re-enable submit button even on error
    if (submitButton) {
        submitButton.disabled = false;
    }
}

// Export functions for global access
window.initializeCheckForm = initializeCheckForm;
