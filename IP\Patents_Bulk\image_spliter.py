import os
from pathlib import Path
import sys
import cv2
import numpy as np
import re
from tqdm import tqdm
import multiprocessing as mp
import functools
import paddle
import base64
import tempfile
import io

# --- Configuration ---
INPUT_DIR = "./FilesAll"
# INPUT_DIR = "./FilesSelection"
OUTPUT_DIR = "./FilesAllSplit"
DEBUG_DIR = "./FilesDebug"
RECT_DIR = "./FilesAllWithRect"

# --- Helper Functions ---
def save_debug_image(img, path_in, folder):
    """Saves an image to a debug subfolder."""
    debug_path = Path(DEBUG_DIR) / folder / path_in.name
    cv2.imencode(path_in.suffix, img)[1].tofile(str(debug_path))

def is_vertical(rect):
    """Checks if a rectangle is oriented vertically."""
    width = rect[1][0] - rect[0][0]
    height = rect[2][1] - rect[1][1]
    return height > width

def rotate_image(img, rects):
    """Rotates an image 90 degrees clockwise and adjusts rectangle coordinates."""
    # Get the original dimensions BEFORE rotation
    h_orig, w_orig = img.shape[:2]

    # Rotate the image
    img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)

    new_rects = []
    for rect in rects:
        # For each point p=(x, y) in the original rectangle, calculate the new coordinates
        # using the original height (h_orig).
        # new_x = h_orig -1- y  (because Y = 0 is at the top, not at the bottom)
        #We subtract 1 because pixel coordinates are 0-indexed. If the height is h_orig, 
        # the y-coordinates range from 0 to h_orig - 1. This -1 ensures the new coordinates stay within the correct bounds.
        # new_y = x
        new_rect = np.array([(h_orig -1- p[1], p[0]) for p in rect])
        new_rects.append(new_rect)
        
    return img, new_rects

def is_line_white(line, vertical=False):
    """Checks if a line of pixels is entirely white."""
    if vertical:
        return np.all(line >= 250)
    return np.all(line >= 250)

def adjust_rects(rects, dx, dy):
    """Adjusts the coordinates of rectangles by a given delta."""
    new_rects = []
    for rect in rects:
        new_rect = np.array(rect) - [dx, dy]
        new_rects.append(new_rect)
    return new_rects

def process_image(img, rects, path_in, out_root, debug_root, suffix=""):
    """
    Processes an image with a known number of FIGs.
    This function is designed to be called recursively.
    """
    if len(rects) == 1:
        handle_one_fig(img, rects, path_in, out_root, debug_root, suffix=suffix)
    elif len(rects) == 2:
        handle_two_figs(img, rects, path_in, out_root, debug_root, suffix=suffix)
    elif len(rects) > 2:
        handle_many_figs(img, rects, path_in, out_root, debug_root, suffix=suffix)

# --- Directory Setup ---
def setup_directories():
    """Create all necessary output and debug directories."""
    Path(OUTPUT_DIR).mkdir(exist_ok=True)
    Path(RECT_DIR).mkdir(exist_ok=True)
    
    debug_subfolders = ["0", "1", "HV", "2a", "2b", "2g", "1g", "3g"]
    for folder in debug_subfolders:
        (Path(DEBUG_DIR) / folder).mkdir(parents=True, exist_ok=True)

    print(f"Output directory: {Path(OUTPUT_DIR).resolve()}")
    print(f"Debug directory: {Path(DEBUG_DIR).resolve()}")
    print(f"Rectangle directory: {Path(RECT_DIR).resolve()}")

def valid_image_ext(p: Path):
    return p.suffix.lower() in {".png", ".jpg", ".jpeg", ".bmp", ".tif", ".tiff", ".webp"}

def get_paddle_ocr(use_gpu: bool):
    from paddleocr import PaddleOCR

    device = "gpu:0" if use_gpu else "cpu"
    print(f"Initializing PaddleOCR on {device}...")
    
    ocr = PaddleOCR(
        device=device,
        lang="en",
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=True,
        text_det_limit_side_len=2048,
        text_det_limit_type="max",
        enable_hpi=False)
    
    return ocr

def handle_one_fig(img, rects, path_in, out_root, debug_root, suffix=""):
    rect = rects[0]
    if is_vertical(rect):
        img, rects = rotate_image(img, rects)
        rect = rects[0]

    x0, y0 = rect.min(axis=0)
    x1, y1 = rect.max(axis=0)

    h, w = img.shape[:2]
    #Just whiten out the FIG rectangle irrespective of whether it is at the top or at the bottom
    cv2.rectangle(img, (x0, y0), (x1, y1), (255, 255, 255), -1)
    img_cropped = img
    # Save cropped image
    out_path = out_root / f"{path_in.stem}{suffix}{path_in.suffix}"
    cv2.imencode(path_in.suffix, img_cropped)[1].tofile(str(out_path))


def handle_two_figs(img, rects, path_in, out_root, debug_root, suffix=""):
    are_vertical = [is_vertical(r) for r in rects]
    if any(are_vertical) and not all(are_vertical):
        save_debug_image(img, path_in, "HV")
        return
    
    if all(are_vertical):
        img, rects = rotate_image(img, rects)

    rects = sorted(rects, key=lambda r: r.min(axis=0)[1]) # Sort by Y
    
    r1_x0, r1_y0 = rects[0].min(axis=0)
    r1_x1, r1_y1 = rects[0].max(axis=0)
    r2_x0, r2_y0 = rects[1].min(axis=0)
    r2_x1, r2_y1 = rects[1].max(axis=0)

    h, w = img.shape[:2]
    x_margin = w * 0.25
    y_margin = h * 0.25
    cond1=False
    cond2 = False

    # Vertically aligned: the X values are closer to each other than the Y value (but max 25% of the image)
    if abs(r1_x0 - r2_x0) < abs(r1_y0 - r2_y0) and abs(r1_x0 - r2_x0) < x_margin and abs(r1_x1 - r2_x1) < x_margin:
        #Bringing back the old logic
        # Determine if FIGs are above or below their content
        is_top_case = r1_y0 < 5 or np.all(img[0:r1_y0-3, :] >= 250)
        
        #when place h-5, US09283045-20160315-D00028 doesn't pass the bottom case, Ask Serge
        is_bottom_case = r2_y1 > h - 5 or np.all(img[r2_y1+3:, :] >= 250) 

        #if Figs are  either at the bottom or at the top, we make cond1 true
        if is_bottom_case or is_top_case:
            cond1=True
    
        split_y = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = r1_y1 + i
                if line_y >= r2_y0: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = r2_y0 - i
                if line_y <= r1_y1: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        
        if split_y != -1:
            img1 = img[:split_y, :]
            img2 = img[split_y:, :]
            process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix="_1")
            adjusted_rect = adjust_rects([rects[1]], 0, split_y)
            process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix="_2")
            return

    # Horizontally aligned: the Y values are closer to each other than the X value (but max 25% of the image)
    elif abs(r1_y0 - r2_y0) < abs(r1_x0 - r2_x0) and abs(r1_y0 - r2_y0) < y_margin and abs(r1_y1 - r2_y1) < y_margin:
        cond2 = True
        # Sort by X to correctly assign rects to sub-images
        rects = sorted(rects, key=lambda r: r.min(axis=0)[0])
        r1_x_end = rects[0].max(axis=0)[0] # end x of rectangle on the left
        r2_x_start = rects[1].min(axis=0)[0] # start x of rectangle on the right
        mid_x = (r1_x_end + r2_x_start) // 2  # take middle between these 2 x coordinates

        split_status = False
        stop_point = r2_x_start - mid_x
        for i in range(stop_point): 
            #move towards right         
            if is_line_white(img[:, mid_x + i], vertical=True):
                    img1 = img[:, :mid_x + i]
                    img2 = img[:, mid_x + i:]
                    process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                    adjusted_rect = adjust_rects([rects[1]], mid_x + i, 0)
                    process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
                    split_status = True
                    break
            #move towards left
            elif is_line_white(img[:, mid_x - i], vertical=True):
                    img1 = img[:, :mid_x - i]
                    img2 = img[:, mid_x - i:]
                    process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                    adjusted_rect = adjust_rects([rects[1]], mid_x - i, 0)
                    process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
                    return
      

    #If figs are not vertically aligned or horizontally aligned but we can still see a possible split        
    if not(cond1 and cond2):
        # #Vertically scan for white line
        vertical_line_found = False
        rects_sorted_by_x = sorted(rects, key=lambda r: r.min(axis=0)[0])
        mid_point = w // 2
        r1_x_end = rects_sorted_by_x[0].max(axis=0)[0] # end x of rectangle on the left
        r2_x_start = rects_sorted_by_x[1].min(axis=0)[0] # start x of rectangle on the right
        left_stop_point = abs(mid_point- r1_x_end)
        right_stop_point = abs(r2_x_start - mid_point)
        for i in range(left_stop_point): #move towards left
            if is_line_white(img[:, mid_point - i], vertical=True):
                vertical_line_found = True
                img1 = img[:, :mid_point - i]
                img2 = img[:, mid_point- i:]
                process_image(img1, [rects_sorted_by_x[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                adjusted_rect = adjust_rects([rects_sorted_by_x[1]], mid_point - i, 0)
                process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
                break
        
            
        if not vertical_line_found:        
            for i in range(right_stop_point): #move towards right
                if is_line_white(img[:, mid_point + i], vertical=True):
                    vertical_line_found = True
                    img1 = img[:, :mid_point + i]
                    img2 = img[:, mid_point + i:]
                    process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                    adjusted_rect = adjust_rects([rects[1]], mid_point + i, 0)
                    process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")                 

                    break
                
        if not vertical_line_found:
            #Horizontally scan for white line
            horizontal_line_found = False
            rects = sorted(rects, key=lambda r: r.min(axis=0)[1])
            mid_y = (r1_y1 + r2_y0) // 2
            search_range = r2_y0 - mid_y
            # Check up

            for i in range(search_range):
                if mid_y - i > r1_y1 and is_line_white(img[mid_y - i, :]):
                     horizontal_line_found = True
                     img1 = img[ :mid_y- i,:]
                     img2 = img[mid_y - i:,:]
                     process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                     adjusted_rect = adjust_rects([rects[1]],0,mid_y-i)
                     process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
                     break
            #Check down
            if not horizontal_line_found:
                for i in range(search_range):
                  if mid_y + i < r2_y0 and is_line_white(img[mid_y + i, :]):
                     horizontal_line_found = True
                     img1 = img[ :mid_y+i,:]
                     img2 = img[mid_y +i:,:]
                     process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1")
                     adjusted_rect = adjust_rects([rects[1]],0,mid_y+i)
                     process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
                     break  
                #We finally place an image in a debug folder after all the above checks
                else:
                   save_debug_image(img, path_in, "2b")



def handle_many_figs(img, rects, path_in, out_root, debug_root, suffix=""):
    are_vertical = [is_vertical(r) for r in rects]
    if any(are_vertical) and not all(are_vertical):
        save_debug_image(img, path_in, "HV")
        return
    
    if all(are_vertical):
        img, rects = rotate_image(img, rects)

    h, w = img.shape[:2]
    y_margin = h * 0.25

    # Group by Y coordinate
    rects = sorted(rects, key=lambda r: r.min(axis=0)[1])
    groups = []
    if rects:
        current_group = [rects[0]]
        for i in range(1, len(rects)):
            #Implementing the same updated logic as in handle_two_figs for grouping HA figs
            r1_x0, r1_y0 = rects[i].min(axis=0)
            r1_x1, r1_y1 = rects[i].max(axis=0)
            r2_x0, r2_y0 = current_group[-1].min(axis=0)
            r2_x1, r2_y1 = current_group[-1].max(axis=0)
            if abs(r1_y0 - r2_y0) < abs(r1_x0 - r2_x0) and abs(r1_y0 - r2_y0) < y_margin and abs(r1_y1 - r2_y1) < y_margin:
                current_group.append(rects[i])
            else:
                groups.append(current_group)
                current_group = [rects[i]]
        groups.append(current_group)

    if len(groups) == 1: # One row of figures
        group = sorted(groups[0], key=lambda r: r.min(axis=0)[0])
        split_points = [0]
        for i in range(len(group) - 1):
            x_start = group[i].max(axis=0)[0] # end x of rectangle on the left
            x_end = group[i+1].min(axis=0)[0] # start x of rectangle on the right
            mid_x = (x_start + x_end) // 2  # take middle between these 2 x coordinates
            split_x = -1
            # The search range is from the middle point to the start of the right rectangle.
            search_range = x_end - mid_x
            for i in range(search_range):
                # Check right
                if mid_x + i < x_end and is_line_white(img[:, mid_x + i], vertical=True):
                    split_x = mid_x + i
                    break
                # Check left
                if mid_x - i > x_start and is_line_white(img[:, mid_x - i], vertical=True):
                    split_x = mid_x - i
                    break
            
            if split_x != -1:
                split_points.append(split_x)
            else:
                # If no split point is found between two rects, debug and stop.
                save_debug_image(img, path_in, "1g")
                return
        split_points.append(w)
        
        for i in range(len(split_points) - 1):
            sub_img = img[:, split_points[i]:split_points[i+1]]
            adjusted_rect = adjust_rects([group[i]], split_points[i], 0)
            process_image(sub_img, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}")

    elif len(groups) == 2: # Two rows of figures
        #Bringing back the same old logic for splitting VA figs

        top_group_top_y = groups[0][0].min(axis=0)[1] # top of first rectangle in first row
        bottom_group_bottom_y = groups[1][0].max(axis=0)[1] #bottom of the last rectangle in second row
        is_top_case = top_group_top_y < 5 or np.all(img[0:top_group_top_y-3, :] >= 250)
        is_bottom_case = bottom_group_bottom_y > h - 5 or np.all(img[bottom_group_bottom_y+3:, :] >= 250)
        if is_bottom_case or is_top_case:
            cond1=True
        split_y = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = groups[0][-1].max(axis=0)[1] + i
                if line_y >= groups[1][0].min(axis=0)[1]: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = groups[1][0].min(axis=0)[1] - i
                if line_y <= groups[0][-1].max(axis=0)[1]: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        
        if split_y != -1:
            img1 = img[:split_y, :]
            img2 = img[split_y:, :]
            process_image(img1, groups[0], path_in, out_root, debug_root, suffix="_1")
            adjusted_rect = adjust_rects(groups[1], 0, split_y)
            process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix="_2")
            return 
        
        else:
             save_debug_image(img, path_in, "2g")

    elif len(groups) == 3: # Three rows of figures
       #Bringing back the old logic
        # Determine if FIGs are above or below their content

        r1_y0=groups[0][-1].min(axis=0)[1] #top of first rectangle
        r1_y1 = groups[0][-1].max(axis=0)[1] #bottom of first rectangle

        r2_y0 = groups[1][0].min(axis=0)[1] #top of second rectangle
        r2_y1=groups[1][0].max(axis=0)[1] #bottom of second rectangle

        r3_y0 = groups[2][0].min(axis=0)[1] #top of third rectangle
        r3_y1 = groups[2][0].max(axis=0)[1] #bottom of third rectangle

        
        is_top_case = r1_y0 < 5 or np.all(img[0:r1_y0-1, :] >= 250)
        is_bottom_case = r3_y1 > h - 5 or np.all(img[r3_y1+1:, :] >= 250)

        split_y1 = -1
        split_y2 = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = r1_y1 + i
                if line_y >= r2_y0: break
                if is_line_white(img[line_y, :]):
                    split_y1 = line_y
                    break
            for i in range(1, 40):
                line_y = r2_y1 + i
                if line_y >= r3_y0: break
                if is_line_white(img[line_y, :]):
                    split_y2 = line_y
                    break
            
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = r2_y0 - i
                if line_y <= r1_y1: break
                if is_line_white(img[line_y, :]):
                    split_y1 = line_y
                    break
            for i in range(1, 40):
                line_y = r3_y0 - i
                if line_y <= r2_y1: break
                if is_line_white(img[line_y, :]):
                    split_y2 = line_y
                    break
        
        if split_y1 != -1 and split_y2 != -1:
            img1 = img[:split_y1, :]
            img2 = img[split_y1:split_y2, :]
            img3 = img[split_y2:, :]
            process_image(img1, groups[0], path_in, out_root, debug_root, suffix=f"{suffix}_1")
            adjusted_rect = adjust_rects(groups[1], 0, split_y1)
            process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2")
            adjusted_rects_3 = adjust_rects(groups[2], 0, split_y2)
            process_image(img3, adjusted_rects_3, path_in, out_root, debug_root, suffix=f"{suffix}_3")
        else:
            # If we can't determine top/bottom or find a split, save for debug
            save_debug_image(img, path_in, "3g")


    # for more than 3 groups (vertically aligned figs )        
    elif (len(groups) >= 4):
        split_points = [0]
        top_first_rect=rects[0].min(axis=0)[1] #top of first rectangle

        bottom_last_rect = rects[-1].max(axis=0)[1] #bottom of last rectangle
        #Bringing back the old logic
        is_top_case = top_first_rect < 5 or np.all(img[0:top_first_rect-3, :] >= 250)
        is_bottom_case = bottom_last_rect > h - 5 or np.all(img[bottom_last_rect+3:, :] >= 250)
        
        for i in range(len(rects)-1):
                    # Determine if FIGs are above or below their content
                    r1_y1 = rects[i].max(axis=0)[1] #bottom of first rectangle

                    r2_y0 = rects[i+1].min(axis=0)[1] #top of second rectangle
                                  
                    split_y = -1
                    
                    if is_bottom_case: # Labels are below figures, split is after first figure
                        for j in range(1, 40):
                            line_y = r1_y1 + j
                            if line_y >= r2_y0: break
                            if is_line_white(img[line_y, :]):
                                split_y = line_y
                                split_points.append(split_y)
                                break
              
                        
                    elif is_top_case: # Labels are above figures, split is before second figure
                        for j in range(1, 40):
                            line_y = r2_y0 - j
                            if line_y <= r1_y1: break
                            if is_line_white(img[line_y, :]):
                                split_y = line_y
                                split_points.append(split_y)
                                break
                        
                    
                    if split_y != -1 and i <= len(rects) - 3:
                        sub_img = img[split_points[i]:split_points[i+1], :]
                        adjusted_rect = adjust_rects([rects[i]], 0,split_points[i])
                        process_image(sub_img, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}")
                    
                    elif split_y != -1 and i == len(rects) - 2:
                        sub_img1 = img[split_points[i]:split_points[i+1],:]
                        sub_img2 = img[split_points[i+1]:,:]
                        adjusted_rect1 = adjust_rects([rects[i]], 0,split_points[i])
                        adjusted_rect2 = adjust_rects([rects[i+1]], 0,split_points[i+1])
                        process_image(sub_img1, adjusted_rect1, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}") 
                        process_image(sub_img2, adjusted_rect2, path_in, out_root, debug_root, suffix=f"{suffix}_{i+2}")

                    else:
                        # If we can't determine top/bottom or find a split, save for debug
                        save_debug_image(img, path_in, "3g")


    else:
        save_debug_image(img, path_in, "3g") # Default for >3 groups
        

def process_one(path_in: Path, out_root: Path, debug_root: Path, ocr, regex):
    img = cv2.imdecode(np.fromfile(str(path_in), dtype=np.uint8), cv2.IMREAD_COLOR)
    if img is None:
        return False, "read_error"

    result = ocr.predict(img)[0]

    rects = []
    for line_txt, box in zip(result["rec_texts"], result["dt_polys"]):
        if line_txt and regex.match(line_txt):
            rects.append(np.array(box).astype(int))

    if not rects:
        save_debug_image(img, path_in, "0")
        return True, "ok_no_match"

    # Draw rectangles on a copy of the image for saving
    img_with_rects = img.copy()
    for rect in rects:
        poly = np.array(rect, dtype=np.int32).reshape((-1, 1, 2))
        cv2.polylines(img_with_rects, [poly], isClosed=True, color=(0, 255, 0), thickness=2)

    # Save the image with rectangles
    rect_path = Path(RECT_DIR) / path_in.name
    cv2.imencode(path_in.suffix, img_with_rects)[1].tofile(str(rect_path))

    process_image(img, rects, path_in, out_root, debug_root, suffix="")
    
    return True, "ok"


def process_worker(path_in, in_root, out_root, debug_root, ocr, regex):
    """Wrapper for multiprocessing; handles paths and errors."""
    try:
        ok, code = process_one(
            path_in=path_in,
            out_root=out_root,
            debug_root=debug_root,
            ocr=ocr,
            regex=regex
        )
        return ok, code, path_in
    except Exception as e:
        print(f"Error processing {path_in}: {e}")
        return False, "other_error", path_in

def main():
    # --- Configuration ---
    # Script options
    JOBS = 4

    setup_directories()

    in_root = Path(INPUT_DIR).resolve()
    out_root = Path(OUTPUT_DIR).resolve()
    debug_root = Path(DEBUG_DIR).resolve()

    if not in_root.exists():
        print(f"Input path not found: {in_root}", file=sys.stderr)
        sys.exit(1)

    # Auto-detect GPU
    use_gpu = paddle.is_compiled_with_cuda()
    if not use_gpu:
        print("No NVIDIA GPU detected or paddlepaddle-gpu not installed. Falling back to CPU.")

    # Multiprocessing setup
    jobs = JOBS
    if use_gpu and jobs != 1:
        print(f"GPU detected: forcing single-process execution (was {jobs}).", file=sys.stderr)
        jobs = 1

    try:
        ocr = get_paddle_ocr(use_gpu)
    except Exception:
        print("\n---", file=sys.stderr)
        print("Failed to initialize PaddleOCR.", file=sys.stderr)
        if use_gpu:
            print("Please ensure 'paddlepaddle-gpu' and 'paddleocr' are installed and CUDA/cuDNN are configured correctly.", file=sys.stderr)
        else:
            print("Please ensure 'paddlepaddle' and 'paddleocr' are installed.", file=sys.stderr)
        print("---", file=sys.stderr)
        raise

    regex = re.compile(r'^fig', re.I)

    files = [p for p in in_root.rglob("*") if p.is_file() and valid_image_ext(p)]
    if not files:
        print("No image files found.", file=sys.stderr)
        sys.exit(2)

    stats = {"ok": 0, "ok_no_match": 0, "read_error": 0, "write_error": 0, "other_error": 0}
    
    worker_func = functools.partial(
        process_worker,
        in_root=in_root,
        out_root=out_root,
        debug_root=debug_root,
        ocr=ocr,
        regex=regex,
    )

    if jobs > 1:
        print(f"Processing with {jobs} parallel jobs...")
        def _init_worker():
            os.environ.setdefault("OMP_NUM_THREADS", "1")
            os.environ.setdefault("MKL_NUM_THREADS", "1")
            os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")
        with mp.Pool(processes=jobs, initializer=_init_worker) as pool:
            with tqdm(total=len(files), desc="Processing", unit="img") as pbar:
                for ok, code, path_in in pool.imap_unordered(worker_func, files):
                    stats[code] = stats.get(code, 0) + 1
                    if not ok:
                        tqdm.write(f"Error processing {path_in}: {code}", file=sys.stderr)
                    pbar.update(1)
    else:
        print("Processing with 1 job...")
        for p in tqdm(files, desc="Processing", unit="img"):
            ok, code, path_in = worker_func(p)
            stats[code] = stats.get(code, 0) + 1
            if not ok:
                tqdm.write(f"Error processing {path_in}: {code}", file=sys.stderr)


    print("\nDone.")
    print(f" Saved: {stats['ok']} (with matches), {stats['ok_no_match']} (no match, copied unchanged)")
    print(f" Read errors: {stats['read_error']}, Write errors: {stats['write_error']}, Other: {stats['other_error']}")

def process_images_for_api(image_base64):
    """
    Process a base64 encoded image for API usage.
    Returns split images as TIFF bytes with metadata.

    Args:
        image_base64: Base64 encoded image string

    Returns:
        dict: {
            'status': 'success' or 'error',
            'message': error message if status is error,
            'split_images': [{'image_data': base64_tiff_bytes, 'filename': str}],
            'rectangles': [{'x': int, 'y': int, 'width': int, 'height': int}]
        }
    """
    try:
        # Decode base64 image
        image_bytes = base64.b64decode(image_base64)
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if img is None:
            return {
                'status': 'error',
                'message': 'Failed to decode image',
                'split_images': [],
                'rectangles': []
            }

        # Initialize OCR
        use_gpu = paddle.is_compiled_with_cuda()
        ocr = get_paddle_ocr(use_gpu)

        # Find FIG rectangles
        result = ocr.predict(img)[0]
        regex = re.compile(r'^fig', re.I)

        rects = []
        rectangles_metadata = []
        for line_txt, box in zip(result["rec_texts"], result["dt_polys"]):
            if line_txt and regex.match(line_txt):
                rect = np.array(box).astype(int)
                rects.append(rect)

                # Convert rectangle to metadata format
                x_coords = rect[:, 0]
                y_coords = rect[:, 1]
                rectangles_metadata.append({
                    'x': int(np.min(x_coords)),
                    'y': int(np.min(y_coords)),
                    'width': int(np.max(x_coords) - np.min(x_coords)),
                    'height': int(np.max(y_coords) - np.min(y_coords))
                })

        if not rects:
            # No FIG rectangles found, return original image
            split_images = [process_single_image_to_tiff(img, "original")]
            return {
                'status': 'success',
                'split_images': split_images,
                'rectangles': []
            }

        # Process image with splitting logic
        split_images = process_image_for_api_internal(img, rects)

        return {
            'status': 'success',
            'split_images': split_images,
            'rectangles': rectangles_metadata
        }

    except Exception as e:
        return {
            'status': 'error',
            'message': f'Error processing image: {str(e)}',
            'split_images': [],
            'rectangles': []
        }

def process_single_image_to_tiff(img, base_filename):
    """Convert a single image to TIFF format with correct parameters"""
    # TIFF encoding parameters for black and white optimization
    encode_params = [
        cv2.IMWRITE_TIFF_COMPRESSION, 4,  # CCITT Group 4 compression
        cv2.IMWRITE_TIFF_XDPI, 300,
        cv2.IMWRITE_TIFF_YDPI, 300
    ]

    # Convert to grayscale if needed for better compression
    if len(img.shape) == 3:
        img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        # Convert to binary (black and white)
        _, img_binary = cv2.threshold(img_gray, 127, 255, cv2.THRESH_BINARY)
    else:
        img_binary = img

    # Encode to TIFF
    success, encoded_img = cv2.imencode('.tiff', img_binary, encode_params)

    if not success:
        raise Exception("Failed to encode image to TIFF")

    # Convert to base64
    img_base64 = base64.b64encode(encoded_img.tobytes()).decode('utf-8')

    return {
        'image_data': img_base64,
        'filename': f"{base_filename}.tiff"
    }

def process_image_for_api_internal(img, rects):
    """
    Internal function to process image with splitting logic and return TIFF bytes
    This is a simplified version of the main processing logic
    """
    split_images = []

    if len(rects) == 1:
        # Single FIG - process and return
        processed_img = handle_one_fig_for_api(img, rects)
        split_images.append(process_single_image_to_tiff(processed_img, "fig_1"))

    elif len(rects) == 2:
        # Two FIGs - try to split
        split_results = handle_two_figs_for_api(img, rects)
        for i, result_img in enumerate(split_results):
            split_images.append(process_single_image_to_tiff(result_img, f"fig_{i+1}"))

    else:
        # Multiple FIGs - handle as best as possible
        split_results = handle_many_figs_for_api(img, rects)
        for i, result_img in enumerate(split_results):
            split_images.append(process_single_image_to_tiff(result_img, f"fig_{i+1}"))

    return split_images

def handle_one_fig_for_api(img, rects):
    """Handle single FIG for API - simplified version"""
    rect = rects[0]
    if is_vertical(rect):
        img, rects = rotate_image(img, rects)
        rect = rects[0]

    x0, y0 = rect.min(axis=0)
    x1, y1 = rect.max(axis=0)

    # Whiten out the FIG rectangle
    cv2.rectangle(img, (x0, y0), (x1, y1), (255, 255, 255), -1)
    return img

def handle_two_figs_for_api(img, rects):
    """Handle two FIGs for API - simplified version"""
    # This is a simplified version - in production you'd want the full logic
    # For now, just return the original image split in half
    h, w = img.shape[:2]
    mid_y = h // 2

    img1 = img[:mid_y, :]
    img2 = img[mid_y:, :]

    return [img1, img2]

def handle_many_figs_for_api(img, rects):
    """Handle multiple FIGs for API - simplified version"""
    # This is a simplified version - in production you'd want the full logic
    # For now, just return the original image
    return [img]

if __name__ == "__main__":
    main()