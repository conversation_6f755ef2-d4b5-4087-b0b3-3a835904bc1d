// Language support and translations
const i18n = {
    en: {
        home: 'Home',
        get_code: 'Get Code',
        documentation: 'Documentation',
        input_form: 'Input Form',
        analysis_report: 'Analysis Report',
        api_key: 'API Key:',
        main_product_image_url: 'Main Product Image URL:',
        upload_main_image: 'Or Upload Main Product Image:',
        other_product_images: 'Other Product Images (Comma separated URLs):',
        upload_other_images: 'Or Upload Other Product Images:',
        ip_images: 'IP Images (Comma separated URLs):',
        upload_ip_images: 'Or Upload IP Images:',
        ip_keywords: 'IP Keywords (comma separated):',
        description: 'Description:',
        reference_text: 'Reference Text:',
        reference_images: 'Reference Images (comma separated URLs):',
        upload_reference_images: 'Or Upload Reference Images:',
        submit: 'Submit',
        clear_images: 'Clear selected images',
        check_id: 'Check ID:',
        risk_level: 'Risk Level:',
        risk_description: 'Risk Description:',
        type: 'Type:',
        ip_owner: 'IP Owner:',
        trademark_text: 'Trademark Text:',
        registration_number: 'Registration Number:',
        serial_number: 'Serial Number:',
        assignee: 'Assignee:',
        applicant: 'Applicant:',
        inventors: 'Inventors:',
        patent_title: 'Title:',
        show_report: 'Show Report',
        hide_report: 'Hide Report',
        product_image: 'Product Image:',
        ip_image: 'IP Image:',
        processing: 'Please wait while your request is being processed...',
        python_code_title: 'Python API Call Code',
        copy_code: 'Copy Code to Clipboard',
        code_copied: 'Code Copied!',
        number_of_cases: 'Number of cases:',
        last_case_docket: 'Last Docket number:',
        last_case_date: 'Last Case Date',
        used_in_tro: 'Has been used in TRO:',
        plaintiff_name: 'Plaintiff Name:',
        no_report: 'No report available.',
        running: 'Running',
        queued: 'In queue',
        minutes: 'min',
        seconds: 'sec',
        estimated_time: 'Estimated time: ',
        error_network: 'A network error occurred. Please check your connection and try again.',
        error_timeout: 'The request timed out. The server may still be processing your request.',
        error_missing_field: 'Missing required field.',
        error_invalid_api_key: 'The provided API Key is invalid or does not exist. Please check and try again.',
        error_rate_limit_minute_exceeded: 'You have exceeded the per-minute request limit. Please try again in a moment.',
        error_rate_limit_daily_exceeded: 'You have exceeded your daily request limit. Please try again tomorrow.',
        error_job_failed: 'The analysis task failed to complete due to an internal error.',
        error_database_connection_error: 'Could not connect to the database. Please try again later.',
        error_results_not_found: 'No results found for the given Check ID. The task may have expired or failed to start.',
        error_qdrant_connection: 'Could not connect to the reverse check service.',
        error_task_submission_error: 'There was an error submitting your request for processing. Please try again.',
        error_missing_main_image: 'A main product image is required for the analysis.',
        error_polling_failed: 'Failed to get the analysis status. Please try again.',
        error_image_download_failed: 'Failed to download one or more images. Please check that the URLs are valid and accessible.',
        detail_invalid_url: 'Reason: One or more image URLs are invalid. Please ensure they point to valid image files (.jpg, .jpeg, .png, .webp).',
        detail_download_failed: 'Reason: Failed to download one or more images from the provided URLs. Please check that they are accessible.',
        detail_base64_decoding_failed: 'Reason: One or more uploaded images could not be processed.',
        detail_empty_image_data: 'Reason: Empty image data was provided.',
        detail_unknown_error: 'Reason: An unknown error occurred during image download.',
        check_page: 'Check',
        reverse_check_page: 'Reverse Check',
        check_history_page: 'Check History',
        fetch_history: 'Fetch History',
        select_date: 'Select Date:',
        check_history: 'Check History',
        check_details: 'Check Details',
        reverse_check_results: 'Reverse Check Results',
        start_date: 'Start Date:',
        end_date: 'End Date:',
        fetch_reverse_check: 'Fetch Reverse Check',
        available_reverse_checks: 'Available Reverse Checks',
        reverse_check_details: 'Reverse Check Details',
        select_reverse_check: 'Select a reverse check from the left panel to view details',
        show_more_results: 'Show more results'
    },
    zh: {
        home: '首页',
        get_code: '获取代码',
        documentation: '文档',
        input_form: '输入表单',
        analysis_report: '分析报告',
        api_key: 'API密钥:',
        main_product_image_url: '主产品图片URL:',
        upload_main_image: '或上传主产品图片:',
        other_product_images: '其他产品图片(逗号分隔URL):',
        upload_other_images: '或上传其他产品图片:',
        ip_images: 'IP图片(逗号分隔URL):',
        upload_ip_images: '或上传IP图片:',
        ip_keywords: 'IP关键词(逗号分隔):',
        description: '产品描述:',
        reference_text: '参考文本:',
        reference_images: '参考图片(逗号分隔URL):',
        upload_reference_images: '或上传参考图片:',
        submit: '提交',
        clear_images: '清除已选图片',
        check_id: '检测ID:',
        risk_level: '风险等级:',
        risk_description: '风险描述:',
        type: '类型:',
        ip_owner: 'IP所有者:',
        trademark_text: '商标文本:',
        registration_number: '注册号:',
        serial_number: '序列号:',
        assignee: '受让人:',
        applicant: '申请人:',
        inventors: '发明人:',
        patent_title: '标题:',
        show_report: '显示报告',
        hide_report: '隐藏报告',
        product_image: '产品图片:',
        ip_image: 'IP图片:',
        processing: '请等待，您的请求正在处理中...',
        python_code_title: 'Python API调用代码',
        copy_code: '复制代码到剪贴板',
        code_copied: '代码已复制!',
        number_of_cases: '案件数量:',
        last_case_docket: '最新案件编号:',
        last_case_date: '最新案件日期',
        used_in_tro: '是否曾用于TRO:',
        plaintiff_name: '原告名称:',
        no_report: '无可用报告。',
        running: '运行中',
        queued: '排队中',
        minutes: '分钟',
        seconds: '秒',
        estimated_time: '预计时间：',
        error_network: '发生网络错误。请检查您的连接并重试。',
        error_timeout: '请求超时，服务器仍在处理您的请求。',
        error_missing_field: '缺少必填字段。',
        error_invalid_api_key: '提供的API密钥无效或不存在。请检查后重试。',
        error_rate_limit_minute_exceeded: '您已超出每分钟的请求限制。请稍后重试。',
        error_rate_limit_daily_exceeded: '您已超出每日请求限制。请明天再试。',
        error_job_failed: '分析任务因内部错误未能完成。',
        error_database_connection_error: '无法连接到数据库。请稍后再试。',
        error_results_not_found: '未找到给定检测ID的结果。任务可能已过期或未能启动。',
        error_qdrant_connection: '无法连接到反向检测服务。',
        error_task_submission_error: '提交您的请求进行处理时出错。请重试。',
        error_missing_main_image: '分析需要主产品图片。',
        error_polling_failed: '获取分析状态失败。请重试。',
        error_image_download_failed: '无法下载一个或多个图片。请检查URL是否有效且可访问。',
        detail_invalid_url: '原因：一个或多个图片链接无效。请确保它们指向有效的图片文件（.jpg、.jpeg、.png、.webp）。',
        detail_download_failed: '原因：无法从提供的URL下载一个或多个图片。请检查它们是否可以访问。',
        detail_base64_decoding_failed: '原因：一个或多个上传的图片无法处理。',
        detail_empty_image_data: '原因：提供了空的图片数据。',
        detail_unknown_error: '原因：图片下载过程中发生未知错误。',
        check_page: '检测',
        reverse_check_page: '反向检测',
        check_history_page: '检测历史',
        fetch_history: '获取历史',
        select_date: '选择日期:',
        check_history: '检测历史',
        check_details: '检测详情',
        reverse_check_results: '反向检测结果',
        start_date: '开始日期:',
        end_date: '结束日期:',
        fetch_reverse_check: '获取反向检测',
        available_reverse_checks: '可用反向检测',
        reverse_check_details: '反向检测详情',
        select_reverse_check: '从左侧面板选择一个反向检测以查看详情',
        show_more_results: '显示更多结果'
    }
};

// Initialize language on page load
function initializeLanguage() {
    const langSelect = document.getElementById('languageSelect');
    const currentLang = document.getElementById('language').value;
    langSelect.value = currentLang;
    langSelect.dispatchEvent(new Event('change'));
}

// Update language for all elements
function updateLanguage(lang) {
    // Update UI elements
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.dataset.i18n;
        el.textContent = i18n[lang][key];
    });

    // Update show/hide report button texts
    document.querySelectorAll('.show-report-button').forEach(button => {
        const reportId = button.dataset.reportId;
        const reportContainer = document.getElementById(reportId);
        if (reportContainer) {
            button.textContent = reportContainer.style.display === 'none' 
                ? i18n[lang].show_report 
                : i18n[lang].hide_report;
        }
    });
}

// Initialize language switcher
function initializeLanguageSwitcher() {
    const languageSelect = document.getElementById('languageSelect');
    const docButton = document.getElementById('documentationButton');

    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            const lang = this.value;
            document.getElementById('language').value = lang;
            updateLanguage(lang);
        });
    }

    if (docButton) {
        docButton.addEventListener('click', function() {
            const lang = document.getElementById('languageSelect').value || 'en';
            const docUrl = `/documentation/${lang}`;
            window.open(docUrl, '_blank');
        });
    }
}

// Export functions for global access
window.i18n = i18n;
window.initializeLanguage = initializeLanguage;
window.updateLanguage = updateLanguage;
window.initializeLanguageSwitcher = initializeLanguageSwitcher;
