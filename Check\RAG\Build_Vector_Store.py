import os
import sys
sys.path.append(os.getcwd())
import numpy as np
from tqdm import tqdm
from DatabaseManagement.ImportExport import get_table_from_GZ
from Check.RAG.Collect_Images import get_case_image_from_df
from IP.Trademarks_Bulk.trademark_db import get_db_connection
import traceback
import uuid

# Qdrant Client
from qdrant_client import QdrantClient, models
from qdrant_client.http.models import Distance, VectorParams

# Qdrant Configuration - Using unified collection structure
QDRANT_API_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None) # Add your API key if Qdrant is secured
QDRANT_COLLECTION_IP_ASSETS = "IP_Assets_Optimized"  # Unified collection for all IP types

qdrant_client = QdrantClient(url=QDRANT_API_URL, api_key=QDRANT_API_KEY)

def build_copyright_images_embeddings_dataset(image_dir, cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_siglip_embeddings
    # List to store embeddings and image identifiers
    embeddings_siglip = []
    reg_nos = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    plaintiff_ids = []
    dockets = []
    number_of_cases_list = []

    # Get list of image files
    image_files = [f for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))]
    processed_keys_in_current_run = set() # Keep track of keys processed in this run
    
    skipped_no_data = []
    skipped_already_processed = []

    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name)):
        existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name))
    else:
        existing_structured_array = None

    # Process each image
    for img_file in tqdm(image_files, desc="Processing images"):
        try:
            img_path = os.path.join(image_dir, img_file)

            case_row, case_image, original_image_key = get_case_image_from_df(cases_df, None, img_file, "copyrights")
            if case_row is None or case_image is None:
                skipped_no_data.append(img_file)
                continue
            
            if original_image_key in processed_keys_in_current_run:
                skipped_already_processed.append(f"{img_file} (key: {original_image_key})")
                continue
            
            plaintiff_id = int(float(case_row['plaintiff_id']))

            if plaintiff_id == 9:
                continue

            embedding_siglip_added = False

            # if existing_structured_array is not None:
            #     matches_in_existing = existing_structured_array[existing_structured_array['filename'] == original_image_key]
            #     if matches_in_existing.size > 0:
            #         # Use the first match if duplicates exist in the NPY file
            #         if 'embedding_siglip' in matches_in_existing.dtype.names:
            #             embeddings_siglip.append(matches_in_existing[0]['embedding_siglip'])
            #             embedding_siglip_added = True
            
            if not embedding_siglip_added:
                # get_siglip_embeddings returns (1, dim), so take [0] for (dim,)
                embeddings_siglip.append(get_siglip_embeddings([img_path], data_type="image")[0])
        
            reg_no_data = case_image["reg_no"]
            if isinstance(reg_no_data, list): # If reg_no_data is a list, take the first element if the list is not empty.
                current_reg_no = reg_no_data[0] if reg_no_data else ""
            else:
                current_reg_no = str(reg_no_data) # Ensure it's a string
            reg_nos.append(current_reg_no)

            full_filename = case_image["full_filename"][0]
            full_filenames.append(full_filename)
            
            
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            plaintiff_names.append(plaintiff_name)

            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
            number_of_cases_list.append(number_of_cases)

            filenames.append(original_image_key) # Store the original image key
            dockets.append(case_row['docket'])
            plaintiff_ids.append(plaintiff_id) # Add plaintiff_id to the list
            processed_keys_in_current_run.add(original_image_key)
            
        except Exception as e:
            print(f"build_images_embeddings_dataset: Error processing {img_file}: {e}, traceback: {traceback.format_exc()}")

    if skipped_no_data:
        print(f"\nbuild_copyright_images_embeddings_dataset: Skipped {len(skipped_no_data)} files because no case/image data was found in df:")
        print(", ".join(skipped_no_data))
    
    if skipped_already_processed:
        print(f"\nSkipped {len(skipped_already_processed)} files as they were already processed in this run for copyright:")
        print(", ".join(skipped_already_processed))
    
    # Convert lists to structured numpy array
    dimensions_siglip = embeddings_siglip[0].shape[0] if embeddings_siglip else 0
    dtype = [('embedding_siglip', 'float32', (dimensions_siglip,)), ('reg_no', 'U100'), ('filename', 'U250'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('plaintiff_id', 'int32'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings_siglip), dtype=dtype)
    
    for i in range(len(embeddings_siglip)):
        structured_array[i] = (embeddings_siglip[i], reg_nos[i], filenames[i], full_filenames[i], plaintiff_names[i], plaintiff_ids[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    EmbeddingsDescriptors_folder = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors')
    os.makedirs(EmbeddingsDescriptors_folder, exist_ok=True)
    np.save(os.path.join(EmbeddingsDescriptors_folder, npy_file_name), structured_array)
    print(f"Copyright embeddings saved to {npy_file_name}")

    # Upload to Qdrant unified collection
    print(f"Uploading {len(structured_array)} copyright embeddings to Qdrant collection: {QDRANT_COLLECTION_IP_ASSETS}")

    # Delete all existing points for this IP type before upserting
    print(f"Deleting existing Copyright points from Qdrant collection: {QDRANT_COLLECTION_IP_ASSETS}")
    qdrant_client.delete(
        collection_name=QDRANT_COLLECTION_IP_ASSETS,
        points_selector=models.FilterSelector(
            filter=models.Filter(
                must=[
                    models.FieldCondition(key="ip_type", match=models.MatchValue(value="Copyright"))
                ]
            )
        ),
        wait=True,
    )
    print("Deletion of existing copyright points complete.")

    points_to_upload = []
    for item in tqdm(structured_array, desc="Preparing copyright points for Qdrant"):
        payload = {key: item[key].item() if hasattr(item[key], 'item') else item[key]
                   for key in item.dtype.names if key != 'embedding_siglip'}
        # Add IP type to payload
        payload['ip_type'] = 'Copyright'

        # Ensure payload values are JSON serializable
        for k, v_item in payload.items():
            if isinstance(v_item, np.integer):
                payload[k] = int(v_item)
            elif isinstance(v_item, np.floating):
                payload[k] = float(v_item)
            elif isinstance(v_item, np.ndarray):
                 payload[k] = v_item.tolist() if v_item.size > 0 else list(v_item)

        # Create deterministic UUID based on filename and IP type
        points_to_upload.append(models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, item['filename'])),
            vector={"siglip_vector": item['embedding_siglip'].tolist()},
            payload=payload
        ))

    # Note: Collection should already exist from setup_qdrant.py

    # Upsert in batches
    batch_size = 500
    for i in tqdm(range(0, len(points_to_upload), batch_size), desc="Uploading copyright points to Qdrant"):
        batch = points_to_upload[i:i+batch_size]
        qdrant_client.upsert(collection_name=QDRANT_COLLECTION_IP_ASSETS, points=batch, wait=True)
    print("Copyright embeddings uploaded to Qdrant.")

    # Set production flag in the database
    # Reset the production flag for all copyright files before setting it for the new ones
    print("Resetting production flag for all copyright files.")
    reset_copyright_production_flag()

    print(f"Setting production flag for {len(filenames)} copyright files in the database.")
    db_batch_size = 500
    for i in tqdm(range(0, len(filenames), db_batch_size), desc="Updating production flag in DB"):
        batch_filenames = filenames[i:i+db_batch_size]
        set_copyright_production_true_batch(batch_filenames)
    print("Production flag update complete.")



def build_patent_images_embeddings_dataset(image_dir, cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_siglip_embeddings
    # List to store embeddings and image identifiers
    embeddings = []
    texts = []
    patent_numbers = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    plaintiff_ids = []
    dockets = []
    number_of_cases_list = []

    # Get list of image files
    image_files = [f for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))]
    processed_keys_in_current_run = set() # Keep track of keys processed in this run
    
    skipped_no_data = []
    skipped_already_processed = []

    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name)):
        existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name))
    else:
        existing_structured_array = None


    # Process each image
    for img_file in tqdm(image_files, desc="Processing images"):
        try:
            img_path = os.path.join(image_dir, img_file)

            # Pass the full img_file from disk to get_case_image_from_df
            case_row, case_image, original_image_key = get_case_image_from_df(cases_df, None, img_file, "patents")
            if case_row is None or case_image is None:
                skipped_no_data.append(img_file)
                continue
            
            if original_image_key in processed_keys_in_current_run:
                skipped_already_processed.append(f"{img_file} (key: {original_image_key})")
                continue
            
            # Check against original_image_key for existing embeddings
            embedding_added_for_this_file = False
            if existing_structured_array is not None:
                matches_in_existing = existing_structured_array[existing_structured_array['filename'] == original_image_key]
                if matches_in_existing.size > 0 and 'embedding_siglip' in matches_in_existing.dtype.names:
                    embeddings.append(matches_in_existing[0]['embedding_siglip'])
                    embedding_added_for_this_file = True
            
            if not embedding_added_for_this_file:
                print(f"🔥 Building embeddings for {img_file} (key: {original_image_key})")
                # get_siglip_embeddings returns (1, dim), so take [0] for (dim,)
                embeddings.append(get_siglip_embeddings([img_path], data_type="image")[0])

            patent_number = case_image["patent_number"]
            patent_numbers.append(patent_number)

            text = case_image["product_name"]
            texts.append(text)

            full_filename = case_image["full_filename"][0]
            full_filenames.append(full_filename)
            
            plaintiff_id = int(float(case_row['plaintiff_id']))
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            plaintiff_names.append(plaintiff_name)

            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
            number_of_cases_list.append(number_of_cases)

            filenames.append(original_image_key) # Store the original image key
            dockets.append(case_row['docket'])
            plaintiff_ids.append(plaintiff_id) # Add plaintiff_id to the list
            processed_keys_in_current_run.add(original_image_key)
            
        except Exception as e:
            print(f"build_images_embeddings_dataset: Error processing {img_file}: {e}, traceback: {traceback.format_exc()}")

    if skipped_no_data:
        print(f"\nbuild_patent_images_embeddings_dataset: Skipped {len(skipped_no_data)} files because no case/image data was found in df:")
        print(", ".join(skipped_no_data))

    if skipped_already_processed:
        print(f"\nSkipped {len(skipped_already_processed)} files as they were already processed in this run for patent images:")
        print(", ".join(skipped_already_processed))
    
    # Convert lists to structured numpy array
    dimensions = embeddings[0].shape[0] if embeddings else 0
    dtype = [('embedding_siglip', 'float32', (dimensions,)), ('text', 'U200'), ('patent_number', 'U100'),('filename', 'U250'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('plaintiff_id', 'int32'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings), dtype=dtype)
    
    for i in range(len(embeddings)):
        structured_array[i] = (embeddings[i], texts[i], patent_numbers[i], filenames[i], full_filenames[i], plaintiff_names[i], plaintiff_ids[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    EmbeddingsDescriptors_folder = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors')
    os.makedirs(EmbeddingsDescriptors_folder, exist_ok=True)
    np.save(os.path.join(EmbeddingsDescriptors_folder, npy_file_name), structured_array)
    print(f"Patent image embeddings saved to {npy_file_name}")

    # Upload to Qdrant unified collection
    print(f"Uploading {len(structured_array)} patent image embeddings to Qdrant collection: {QDRANT_COLLECTION_IP_ASSETS}")
    points_to_upload = []
    for item in tqdm(structured_array, desc="Preparing patent image points for Qdrant"):
        payload = {key: item[key].item() if hasattr(item[key], 'item') else item[key]
                   for key in item.dtype.names if key != 'embedding_siglip'}
        # Add IP type to payload
        payload['ip_type'] = 'Patent'
        payload['data_type'] = 'image'  # Distinguish from patent text

        for k, v_item in payload.items(): # Ensure JSON serializable
            if isinstance(v_item, np.integer): payload[k] = int(v_item)
            elif isinstance(v_item, np.floating): payload[k] = float(v_item)
            elif isinstance(v_item, np.ndarray): payload[k] = v_item.tolist() if v_item.size > 0 else list(v_item)

        points_to_upload.append(models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"patent_image_{item['filename']}")),
            vector={"siglip_vector": item['embedding_siglip'].tolist()},
            payload=payload
        ))

    # Note: Collection should already exist from setup_qdrant.py

    batch_size = 100
    for i in tqdm(range(0, len(points_to_upload), batch_size), desc="Uploading patent image points to Qdrant"):
        batch = points_to_upload[i:i+batch_size]
        qdrant_client.upsert(collection_name=QDRANT_COLLECTION_IP_ASSETS, points=batch, wait=True)
    print("Patent image embeddings uploaded to Qdrant.")





def build_patent_text_embeddings_dataset(cases_df, plaintiffs_df, npy_file_name):
    from Check.RAG.RAG_Inference import get_siglip_embeddings
    # List to store embeddings and image identifiers
    embeddings = []
    patent_numbers = []
    full_filenames = []
    filenames = []
    texts = []
    plaintiff_names = []
    plaintiff_ids = []
    dockets = []
    number_of_cases_list = []
    processed_texts_in_current_run = set()

    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name)):
        existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), allow_pickle=True)
    else:
        existing_structured_array = None

    # Get list of image files
    product_name_list = []
    for index, row in tqdm(cases_df.iterrows(), desc="Processing Patent Texts"):
        try:
            if row['images'] is not None and "patents" in row['images']:
                for i, image in enumerate(row['images']['patents'].keys()):
                    if "product_name" in row['images']['patents'][image] and row['images']['patents'][image]['product_name'] != "":
                        text = row['images']['patents'][image]['product_name']
                
                        if text in processed_texts_in_current_run:
                            # print(f"Skipping text '{text}' as it's already processed in this run for patent texts.")
                            continue

                        # Check if the text already exists in current lists (from this run) before appending
                        if text != "" and text is not None: # Basic check for valid text
                            patent_number = row['images']['patents'][image]['patent_number']
                            print(f"patent_number: {patent_number}, patent text: {text}, case: {row["docket"]}")
                            docket = row["docket"]
                            full_filename = row['images']['patents'][image]['full_filename'][0]
                            filename = [key for key in row['images']['patents'].keys() if row['images']['patents'][key]['full_filename'][0] == full_filename]
                            plaintiff_id = int(float(cases_df[cases_df['docket'] == docket]['plaintiff_id'].values[0]))
                            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
                            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]
                            
                            # Append embedding and metadata to lists
                            embedding_added_for_this_text = False
                            if existing_structured_array is not None:
                                # Match by text content for existing entries
                                matches_in_existing = existing_structured_array[(existing_structured_array['text'] == text) & (existing_structured_array['docket'] == docket)]
                                if matches_in_existing.size > 0 and 'embedding_siglip' in matches_in_existing.dtype.names:
                                    embeddings.append(matches_in_existing[0]['embedding_siglip'])
                                    embedding_added_for_this_text = True
                            
                            if not embedding_added_for_this_text:
                                print(f"🔥 Building embeddings for text: {text}")
                                embeddings.append(get_siglip_embeddings([text], data_type="text")[0])
            

                            patent_numbers.append(patent_number)
                            full_filenames.append(full_filename)
                            filenames.append(filename) # Each item is a list of filenames
                            texts.append(text)
                            plaintiff_names.append(plaintiff_name)
                            dockets.append(docket)
                            plaintiff_ids.append(plaintiff_id) # Add plaintiff_id to the list
                            number_of_cases_list.append(number_of_cases)
                            processed_texts_in_current_run.add(text)
                    else: 
                        print(f"No product text for case:{row["docket"]}")
            
        except Exception as e:
            print(f"build_text_embeddings_dataset: Error processing {row['docket']}: {e}, traceback: {traceback.format_exc()}")
    
    # Convert lists to structured numpy array
    dimensions = embeddings[0].shape[0] if embeddings else 0
    dtype = [('embedding_siglip', 'float32', (dimensions,)), ('text', 'U200'), ('patent_number', 'U100'), ('filename', 'O'), ('full_filename', 'U250'), ('plaintiff_name', 'U150'), ('plaintiff_id', 'int32'), ('docket', 'U100'), ('number_of_cases', 'int')]
    structured_array = np.zeros(len(embeddings), dtype=dtype)
    
    for i in range(len(embeddings)):
        structured_array[i] = (embeddings[i], texts[i], patent_numbers[i], filenames[i], full_filenames[i], plaintiff_names[i], plaintiff_ids[i], dockets[i], number_of_cases_list[i])

    # Save structured array to disk
    EmbeddingsDescriptors_folder = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors')
    os.makedirs(EmbeddingsDescriptors_folder, exist_ok=True)
    np.save(os.path.join(EmbeddingsDescriptors_folder, npy_file_name), structured_array)
    print(f"Patent text embeddings saved to {npy_file_name}")

    # Upload to Qdrant unified collection
    print(f"Uploading {len(structured_array)} patent text embeddings to Qdrant collection: {QDRANT_COLLECTION_IP_ASSETS}")
    points_to_upload = []
    for item in tqdm(structured_array, desc="Preparing patent text points for Qdrant"):
        payload = {key: item[key].item() if hasattr(item[key], 'item') else item[key]
                   for key in item.dtype.names if key != 'embedding_siglip'}
        # Add IP type to payload
        payload['ip_type'] = 'Patent'
        payload['data_type'] = 'text'  # Distinguish from patent image

        for k, v_item in payload.items(): # Ensure JSON serializable
            if isinstance(v_item, np.integer): payload[k] = int(v_item)
            elif isinstance(v_item, np.floating): payload[k] = float(v_item)
            elif isinstance(v_item, np.ndarray): payload[k] = v_item.tolist() if v_item.size > 0 else list(v_item) # Handles 'filename' being object
            elif isinstance(v_item, list): payload[k] = v_item # Already a list

        # Create a unique ID for text entries, e.g., by using text and patent number
        text_id_str = f"patent_text_{item['text']}_{item['patent_number']}"

        points_to_upload.append(models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, text_id_str)),
            vector={"siglip_vector": item['embedding_siglip'].tolist()},
            payload=payload
        ))
    batch_size = 100
    for i in tqdm(range(0, len(points_to_upload), batch_size), desc="Uploading patent text points to Qdrant"):
        batch = points_to_upload[i:i+batch_size]
        qdrant_client.upsert(collection_name=QDRANT_COLLECTION_IP_ASSETS, points=batch, wait=True)
    print("Patent text embeddings uploaded to Qdrant.")



def build_trademark_logo_embeddings_dataset(trademark_dir, cases_df, plaintiffs_df, npy_file_name):
    """Build trademark logo embeddings using SigLIP instead of ORB descriptors."""
    from Check.RAG.RAG_Inference import get_siglip_embeddings


    if os.path.exists(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name)):
        existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', npy_file_name), allow_pickle=True)
    else:
        existing_structured_array = None

    # Get list of image files
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp')
    image_files = [f for f in os.listdir(trademark_dir) if f.lower().endswith(valid_extensions)]

    # Initialize metadata lists
    embeddings_siglip = []
    filenames = []
    full_filenames = []
    plaintiff_names = []
    plaintiff_ids = []
    dockets = []
    number_of_cases_list = []
    reg_nos = []
    int_cls_list = []
    processed_keys_in_current_run = set()
    
    skipped_no_data = []
    skipped_already_processed = []

    # Process each image
    for img_file in tqdm(image_files, desc="Processing trademarks"):
        try:
            img_path = os.path.join(trademark_dir, img_file)

            case_row, case_image, original_image_key = get_case_image_from_df(cases_df, None, img_file, "trademarks")
            if case_row is None or case_image is None:
                skipped_no_data.append(img_file)
                continue
            
            if original_image_key in processed_keys_in_current_run:
                skipped_already_processed.append(f"{img_file} (key: {original_image_key})")
                continue

            plaintiff_id = int(float(case_row['plaintiff_id']))

            # Get trademark details
            reg_no = case_image["reg_no"]
            int_cls = case_image["int_cls_list"]
            full_filename = case_image["full_filename"]

            # Get case metadata
            plaintiff_name = plaintiffs_df[plaintiffs_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
            number_of_cases = cases_df[cases_df['plaintiff_id'] == plaintiff_id].shape[0]

            embedding_added_for_this_file = False
            if existing_structured_array is not None:
                matches_in_existing = existing_structured_array[existing_structured_array['filename'] == original_image_key]
                if matches_in_existing.size > 0 and 'embedding_siglip' in matches_in_existing.dtype.names:
                    embeddings_siglip.append(matches_in_existing[0]['embedding_siglip'])
                    embedding_added_for_this_file = True

            if not embedding_added_for_this_file:
                print(f"🔥 Building SigLIP embeddings for {img_file}")
                # Generate SigLIP embeddings for trademark image
                embeddings_siglip.append(get_siglip_embeddings([img_path], data_type="image")[0])

            # Store data
            filenames.append(original_image_key) # Store the original image key
            full_filenames.append(full_filename)
            plaintiff_names.append(plaintiff_name)
            plaintiff_ids.append(plaintiff_id)
            dockets.append(case_row['docket'])
            number_of_cases_list.append(number_of_cases)
            reg_nos.append(reg_no)
            int_cls_list.append(int_cls)
            processed_keys_in_current_run.add(original_image_key)

        except Exception as e:
            print(f"Error processing {img_file}: {e}, traceback: {traceback.format_exc()}")

    if skipped_no_data:
        print(f"\nWarning: Could not retrieve case row for {len(skipped_no_data)} files. Skipping:")
        print(", ".join(skipped_no_data))

    if skipped_already_processed:
        print(f"\nSkipped {len(skipped_already_processed)} files as they were already processed in this run for trademarks:")
        print(", ".join(skipped_already_processed))

    # Create structured array with SigLIP embeddings
    dimensions_siglip = embeddings_siglip[0].shape[0] if embeddings_siglip else 0
    dtype = [('embedding_siglip', 'float32', (dimensions_siglip,)), ('filename', 'U250'), ('full_filename', 'O'), ('plaintiff_name', 'U150'), ('plaintiff_id', 'int32'), ('docket', 'U100'), ('number_of_cases', 'int32'), ('reg_no', 'O'), ('int_cls_list', 'O')]

    structured_array = np.zeros(len(filenames), dtype=dtype)
    for i in range(len(filenames)):
        structured_array[i] = (embeddings_siglip[i], filenames[i], full_filenames[i], plaintiff_names[i], plaintiff_ids[i], dockets[i], number_of_cases_list[i], reg_nos[i], int_cls_list[i])

    # Save structured array
    EmbeddingsDescriptors_dir = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors')
    os.makedirs(EmbeddingsDescriptors_dir, exist_ok=True)
    np.save(os.path.join(EmbeddingsDescriptors_dir, npy_file_name), structured_array)
    print(f"Trademark embeddings saved to {npy_file_name}")

    # Upload to Qdrant unified collection
    print(f"Uploading {len(structured_array)} trademark embeddings to Qdrant collection: {QDRANT_COLLECTION_IP_ASSETS}")
    points_to_upload = []
    for item in tqdm(structured_array, desc="Preparing trademark points for Qdrant"):
        payload = {key: item[key].item() if hasattr(item[key], 'item') else item[key]
                   for key in item.dtype.names if key != 'embedding_siglip'}
        # Add IP type to payload
        payload['ip_type'] = 'Trademark'

        # Ensure payload values are JSON serializable (especially for 'O' type fields)
        for k, v_item in payload.items():
            if isinstance(v_item, np.integer): payload[k] = int(v_item)
            elif isinstance(v_item, np.floating): payload[k] = float(v_item)
            elif isinstance(v_item, np.ndarray): payload[k] = v_item.tolist() if v_item.size > 0 else list(v_item)
            elif isinstance(v_item, list): payload[k] = v_item

        points_to_upload.append(models.PointStruct(
            id=str(uuid.uuid5(uuid.NAMESPACE_DNS, f"trademark_{item['filename']}")),
            vector={"siglip_vector": item['embedding_siglip'].tolist()},
            payload=payload
        ))

    # Note: Collection should already exist from setup_qdrant.py

    # Upsert in batches
    batch_size = 100
    for i in tqdm(range(0, len(points_to_upload), batch_size), desc="Uploading trademark points to Qdrant"):
        batch = points_to_upload[i:i+batch_size]
        qdrant_client.upsert(collection_name=QDRANT_COLLECTION_IP_ASSETS, points=batch, wait=True)
    print("Trademark embeddings uploaded to Qdrant.")
    # print("Trademark metadata uploaded to Qdrant (no vectors).")



def investigate_npy():
    existing_structured_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsPatentImages.npy"), allow_pickle=True)
    existing_structured_array_old = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', "EmbeddingsPatentImages copy.npy"), allow_pickle=True)

    print(f"length of new: {len(existing_structured_array)}")
    print(f"length of old: {len(existing_structured_array_old)}")

    for i in range(len(existing_structured_array)):
        if existing_structured_array[i]['full_filename'] != existing_structured_array_old[i]['full_filename']:
            print(f"difference at {i}: {existing_structured_array[i]['full_filename']} != {existing_structured_array_old[i]['full_filename']}")
        if existing_structured_array[i]['full_filename'][-4:] != 'webp':
            print(f"not webp at {i}: {existing_structured_array[i]['full_filename']}")


def set_copyright_production_true_batch(filenames):
    # Update the production flag to true for a batch of copyright files.
    
    if not filenames:
        return 0
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # SQL to update and return the filenames of the rows that were updated
        sql = """
        UPDATE copyrights_files
        SET production = TRUE
        WHERE filename = ANY(%s)
        RETURNING filename
        """

        cursor.execute(sql, (filenames,))
        updated_files = {row[0] for row in cursor.fetchall()}
        updated_count = len(updated_files)
        
        conn.commit()

        if updated_count < len(filenames):
            not_found_files = set(filenames) - updated_files
            for f in not_found_files:
                print(f"File not found in database, could not set production=true: {f}")

        print(f"Set 'production'=true for {updated_count} copyright files.")
        return updated_count

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error updating copyright files' production status: {str(e)}")
        return 0
    finally:
        if conn:
            conn.close()


def reset_copyright_production_flag():
    """Resets the production flag to false for all copyright files."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # SQL to update all rows in copyrights_files to set production to FALSE
        sql = "UPDATE copyrights_files SET production = FALSE"
        
        cursor.execute(sql)
        updated_count = cursor.rowcount
        conn.commit()
        
        print(f"Reset 'production' flag to false for {updated_count} copyright files.")
        return updated_count

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error resetting copyright files' production status: {str(e)}")
        return 0
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    cases_df = get_table_from_GZ("tb_case", force_refresh=False)
    plaintiffs_df = get_table_from_GZ('tb_plaintiff', force_refresh=False)
    BASE_DIR = os.getcwd()

    # investigate_npy()

    ### Patent vector store build
    # PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "patents", "Production")
    # build_patent_images_embeddings_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "EmbeddingsPatentImages.npy")
    # build_patent_text_embeddings_dataset(cases_df, plaintiffs_df, "EmbeddingsPatentTexts.npy")

    ### Trademark vector store build
    PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "trademarks", "Production")
    build_trademark_logo_embeddings_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "EmbeddingsTrademarkLogo.npy")
    
    ### Copyright vector store build
    # PRODUCTION_FOLDER = os.path.join(BASE_DIR, "data", "IP", "copyrights", "Production")
    # build_copyright_images_embeddings_dataset(PRODUCTION_FOLDER, cases_df, plaintiffs_df, "EmbeddingsCopyright.npy")


    ### Test the copyright vector store
    # query_image_path = "D:/Win10User/Downloads/68_A_Stanislav Yurievich Osipov.jpg"
    # query_image_path = "D:/Win10User/Downloads/1_A_GOPRO_cropped.jpg"
    # results = find_most_similar_copyright(query_image_path, top_n=3, similarity_threshold=0.4)

    # if results:
    #     print("Top matches:")
    #     for match in results:
    #         print(f"Image: {match['filename']}")
    #         print(f"Similarity: {match['similarity']:.2f}")
    #         print(f"Plaintiff: {match['plaintiff_name']}")
    #         print(f"Docket: {match['docket']}")
    #         print(f"Total Cases: {match['number_of_cases']}")
    #         print("-" * 50)
    # else:
    #     print("No similar images found above the threshold.")