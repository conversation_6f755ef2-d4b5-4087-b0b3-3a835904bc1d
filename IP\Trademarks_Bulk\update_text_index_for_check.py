# build_and_persist.py  (run in CI or a cron when TM table changes)
import psycopg2, ahocorasick, gzip, zstandard, pickle, unicodedata, re, traceback, os
from IP.Trademarks_Bulk.trademark_db import get_db_connection

def update_trademarkindex():
    try:
        # Determine table name based on debug mode
        is_dev_mode = os.getenv('DEBUG_SYNC_MODE', 'false').lower() == 'true'
        table_name = "trademarks_precomputed_marks_dev" if is_dev_mode else "trademarks_precomputed_marks"
        print(f"Starting truncating {table_name} table...")
        
        conn = get_db_connection()
        with conn, conn.cursor() as c2:
            # Create the table if it doesn't exist
            c2.execute(f"""
                CREATE TABLE IF NOT EXISTS {table_name} (
                    id INTEGER PRIMARY KEY,
                    built_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    automaton_blob OID
                );
            """)
            # Empty the table before inserting new data
            c2.execute(f"TRUNCATE TABLE {table_name};")
            conn.commit() # Commit the TRUNCATE operation
        
        print(f"Table {table_name} truncated. Starting SELECT FROM trademarks table...")

        # SQL command: fet list of trardmark text, without duplicates (keep the one with plaintiff_id, and the most recent one only)
        # Why plaintiff_id IS NULL? In Postgres, FALSE sorts before TRUE, so rows with a plaintiff (FALSE) win over those without.
        sql_query = """
        SELECT
            LOWER(mark_text) AS mark_text,
            plaintiff_id,
            reg_no,
            ser_no,
            applicant_name,
            int_cls,
            goods_services_text_daily
        FROM trademarks
        WHERE mark_text IS NOT NULL AND mark_text <> '' AND mark_feature_code NOT IN (2, 3, 5);
        """
        cur  = conn.cursor(name="tm_cur")     # server-side cursor, streams rows
        cur.execute(sql_query)
        
        print("SELECT FROM trademarks executed. Starting building automaton...")

        A = ahocorasick.Automaton()
        meta = {}  # id → (normalized_mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily)
        # Use a temporary dictionary to group metadata by normalized text
        grouped_texts = {}

        norm = lambda s: re.sub(r'\s+', ' ', unicodedata.normalize('NFKC', s).lower()).strip()

        for idx, row in enumerate(cur):
            mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily = row
            text = norm(mark_text)
            
            # Store the full metadata for this specific trademark instance
            meta[idx] = (text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily)
            
            # Group the unique index (idx) by the normalized text
            if text not in grouped_texts:
                grouped_texts[text] = []
            grouped_texts[text].append(idx)

        # Now, build the automaton from the grouped texts
        for text, ids in grouped_texts.items():
            A.add_word(text, ids)  # Associate the text with a list of all its IDs

        A.make_automaton()
        
        print("Automaton built. Starting compression...")

        # Perform compression and size comparison
        pickled_data = pickle.dumps((A, meta), protocol=pickle.HIGHEST_PROTOCOL)
        
        # zstd_blob = zstandard.compress(pickled_data)
        # Custom compression level: default = 3, max = 22
        cctx = zstandard.ZstdCompressor(level=10)
        zstd_blob = cctx.compress(pickled_data)
        # gzip_blob = gzip.compress(pickled_data)

        # print(f"Size comparison:")
        # print(f"  - Zstandard: {len(zstd_blob) / (1024*1024):.2f} MB")
        # print(f"  - Gzip: {len(gzip_blob) / (1024*1024):.2f} MB")

        # Use the zstd blob for storage
        blob = zstd_blob
        
        print("Compression done. Starting writing to large object...")

        # --- Changes for Large Object handling ---
        with conn: # Use conn as a context manager for transaction
            # Create a new large object
            lo = conn.lobject(0, 'wb') # 0 for new object, 'wb' for write binary
            oid = lo.oid # Get the OID of the new large object
            lo.write(blob) # Write the blob data to the large object
            lo.close() # Close the large object

            # Insert the OID into the table
            with conn.cursor() as c2:
                c2.execute(f"""
                    INSERT INTO {table_name} (id, built_at, automaton_blob)
                    VALUES (1, now(), %s);
                """, (oid,)) # Pass the OID
            conn.commit() # Commit the INSERT operation
            
        print("Trademarks index updated successfully.")
        # --- End of changes ---
    except Exception as e:
        print(f"Error updating trademark index: {str(e)}, traceback: {traceback.format_exc()}")
        if conn:
            conn.rollback()
        raise
        
        
if __name__ == "__main__":
    import os
    os.environ['DEBUG_SYNC_MODE'] = 'True' # update trademarks_precomputed_marks_dev
    update_trademarkindex()