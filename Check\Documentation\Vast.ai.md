Image: Pytorch (and map port 5000 : but to what?)

# Access the server

* Add publick ssh key (.pub) in the vast console
* Convert the private ssh key to pkk and add to Putty
* Use the private key (id_rsa) in vscode for remote session

# Setup: Get / update the code

### Setup

* git init
* git remote add origin https://forgejo.sergedc.com/sergedc/TRO-USside.git
* git fetch origin
* git reset --hard origin/main
* chmod 600 /workspace/.ssh/id_rsa
* copy .env, google cloud service accounts, and ssl folder
* sudo apt update -> sudo apt install redis-server   (then check with sudo systemctl status redis-server)
* pip install -r requirements.txt
* in the vast UI, change entrypoint to add && /root/onstart.sh
* chmod +x /root/onstart.sh
* copy vast_reboot.sh into root/onstart.sh (then reboot and check tmux ls and tail -f /var/log/onstart.log)
* To disable auto-tmux, run `touch ~/.no_auto_tmux` and reconnect.
* Not needed: solve package dependencies conflict, e.g. for vastai/pytorch:2.5.1-cuda-12.1.1
* Not needed: source ./set_env.sh

### Update:

* git fetch origin
* git reset --hard origin/main   (does not delete extra files)
* chmod 600 /workspace/.ssh/id_rsa
* source ./set_env.sh
* Use Vertex for china  (useVertexAI = True on line 260 of GC_vertexai.py)

# Free up VRAM

sudo fuser -v /dev/nvidia*
look for 279821 F...m python
sudo kill -9 279821
nvidia-smi will show how much is used

# Kill app

* ps aux | grep app_apistudio.py
* kill -15 3650  (where 3650 is the pid of app.py)
* or just: pkill -9 -f 'grep app_apistudio.py'

# Cloudflare tunnel

Check cloudflared is installed: cloudflared -version
Install the clouflared tunnel using the instructions on cloudflare website: i.e. cloudflared service install xxxxxxxxxx
cloudflared service install eyJhIjoiYzA3NWM0MzIxMjJkZjliY2ZmMjJkZGVmMzlkZjAxNzAiLCJ0IjoiYzNlMGRiZWItZDdiNi00ODViLTlkNzctOGRmNGFiNTc3YjM2IiwicyI6Ik4ySTVPRFE1TlRJdE5XUXhNaTAwTmpVNUxUZ3haalF0TlRZMk1ETXlZVEV5TWpZeCJ9
Add the public hostname and WAIT (it takes 1 minutes to actually work)
If cloudflared is down: sudo service cloudflared restart

# Others

Setup NAS access:  chmod 600 /workspace/.ssh/id_rsa
Setup tessaract: sudo apt update & sudo apt install tesseract-ocr

# Ensure everything is working on boot:

Recover HDD spac:
rm -rf ~/.local/share/Trash/files/* ~/.local/share/Trash/info/*
What you delete in jupyterlab file explorer goes to trash
What you delete in python using shutil.rmtree does not go to trash
pip cache purge
gio trash --empty (?)
sudo docker system prune -a
See docker used spaces: docker system df -v
docker ps -s
To see space: sudo apt update ⇒ sudo apt install ncdu ⇒ ncdu /
Check HDD space:
sudo apt update
sudo apt install ncdu
ncdu / # Scan the entire root filesystem (might take time, needs sudo for full access)
ncdu /var # Scan the /var directory
ncdu # Scan the current directory

On reboot: source ./set_env.sh + service cloudflared start + restart app.py
How: copy the content of reboot.sh to /root/onstart.sh
