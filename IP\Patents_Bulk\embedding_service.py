import asyncio
import aiohttp
import os
import json
import zlib
import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance
from logdata import log_message
import base64
from Common.uuid_utils import generate_uuid
from pathlib import Path
import psycopg2
from IP.Patents_Bulk.patent_db_grant import get_db_connection


BATCH_SIZE = 50

class EmbeddingQueue:
    def __init__(self, max_concurrent=5, num_workers=10):
        self.queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.client = QdrantClient(url=os.environ.get("QDRANT_URL"), api_key=os.environ.get("QDRANT_API_KEY"), timeout=30)
        self.collection_name = "IP_Assets"
        self.num_workers = num_workers
        self.workers = []
        self.shutdown_event = asyncio.Event()
        self._ensure_collection()

    async def drain(self):
        """Wait until queue is empty and all tasks are processed"""
        await self.queue.join()
        log_message("All embedding queue tasks completed", level='INFO')
    
    def _ensure_collection(self):
        """Create collection if it doesn't exist"""
        collections = self.client.get_collections().collections
        if not any(c.name == self.collection_name for c in collections):
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=1024, distance=Distance.COSINE)
            )
            log_message(f"Created Qdrant collection: {self.collection_name}", level='INFO')
    
    
    async def process_batch(self, batch):
        """Process batch of images (original method for non-splitting workflow)"""
        # Batch check for existing embeddings
        point_ids = [item[1] for item in batch]
        try:
            existing_points = self.client.retrieve(
                collection_name=self.collection_name,
                ids=point_ids,
                with_vectors=False
            )
            existing_ids = {point.id for point in existing_points}
        except Exception as e:
            log_message(f"Qdrant batch check failed: {str(e)}", level='ERROR')
            existing_ids = set()

        to_process = []
        for item in batch:
            ip_type, point_id, image_path, ser_no = item
            if point_id not in existing_ids:
                to_process.append(item)
            # else:
            #     log_message(f"Skipping existing embedding: {ser_no}", level='DEBUG')

        if not to_process:
            return

        # Prepare API request
        API_URL = "https://api.maidalv.com/get_image_embeddings"
        API_KEY = os.getenv("API_BEARER_TOKEN")
        headers = {"Content-Type": "application/json"}

        images_base64 = []
        valid_items = []
        for ip_type, point_id, image_path, ser_no in [(item[0], item[1], item[2], item[3]) for item in to_process]: # Extract point_id and ser_no
            if os.path.exists(image_path):
                try:
                    with open(image_path, "rb") as img_file:
                        images_base64.append(base64.b64encode(img_file.read()).decode('utf-8'))
                    valid_items.append((ip_type, point_id, ser_no))
                except Exception as e:
                    log_message(f"Error reading {image_path}: {str(e)}", level='ERROR')
            else:
                log_message(f"Image missing: {image_path}", level='WARNING')

        if not images_base64:
            return

        payload = {
            "api_key": API_KEY,
            "images": images_base64
        }

        # Get embeddings
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(API_URL, json=payload, headers=headers) as resp:
                    if resp.status != 200:
                        log_message(f"API error: {resp.status} {await resp.text()}", level='ERROR')
                        return

                    result = await resp.json()
                    if result.get('status') == 'success':
                        # Decode and decompress embeddings
                        encoded_compressed_embeddings = result.get('embeddings')
                        compressed_embeddings = base64.b64decode(encoded_compressed_embeddings)
                        embeddings_bytes = zlib.decompress(compressed_embeddings)

                        # Convert bytes back to numpy array
                        embeddings = np.frombuffer(embeddings_bytes, dtype=np.float32).reshape(-1, 1024)

                        if len(embeddings) != len(valid_items):
                            log_message(f"Embedding count mismatch: expected {len(valid_items)}, got {len(embeddings)}", level='ERROR')
                            return

                        points = [
                            PointStruct(
                                id=item[1],
                                vector={"siglip_vector": embedding.tolist()},  # Convert numpy array to list
                                payload={
                                    "ip_type": item[0],
                                    "reg_no": item[2]
                                }
                            )
                            for item, embedding in zip(valid_items, embeddings)
                        ]
                        self.client.upsert(
                            collection_name=self.collection_name,
                            points=points,
                            wait=True
                        )
                        queue_size = self.queue.qsize()
                        log_message(f"Uploaded {len(points)} embeddings to Qdrant, {queue_size} items remaining in queue", level='INFO')
                    else:
                        log_message(f"Embedding API error: {result.get('message', 'Unknown error')}", level='ERROR')
        except Exception as e:
            log_message(f"Embedding processing failed: {str(e)}", level='ERROR')

    async def process_batch_with_splitting(self, batch):
        """Process batch of images with splitting workflow"""
        if not batch:
            return

        # Prepare API request for splitting and embedding
        API_URL = "https://api.maidalv.com/get_images_split_with_embeddings"
        API_KEY = os.getenv("API_BEARER_TOKEN")
        headers = {"Content-Type": "application/json"}

        images_base64 = []
        valid_items = []
        for ip_type, reg_no, image_path in batch:
            if os.path.exists(image_path):
                try:
                    with open(image_path, "rb") as img_file:
                        images_base64.append(base64.b64encode(img_file.read()).decode('utf-8'))
                    valid_items.append((ip_type, reg_no, image_path))
                except Exception as e:
                    log_message(f"Error reading {image_path}: {str(e)}", level='ERROR')
            else:
                log_message(f"Image missing: {image_path}", level='WARNING')

        if not images_base64:
            return

        payload = {
            "api_key": API_KEY,
            "images": images_base64
        }

        # Get split images and embeddings
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(API_URL, json=payload, headers=headers) as resp:
                    if resp.status != 200:
                        log_message(f"API error: {resp.status} {await resp.text()}", level='ERROR')
                        return

                    result = await resp.json()
                    if result.get('status') == 'success':
                        # Process each image result
                        image_results = result.get('results', [])

                        for i, (ip_type, reg_no, original_image_path) in enumerate(valid_items):
                            if i >= len(image_results):
                                log_message(f"Missing result for image {original_image_path}", level='ERROR')
                                continue

                            image_result = image_results[i]
                            split_images = image_result.get('split_images', [])
                            embeddings_data = image_result.get('embeddings', [])

                            if not split_images:
                                log_message(f"No split images returned for {original_image_path}", level='WARNING')
                                continue

                            # Save split images and collect new filenames
                            new_filenames = []
                            points_to_upsert = []

                            original_path = Path(original_image_path)
                            original_stem = original_path.stem

                            for j, (split_image_data, embedding_data) in enumerate(zip(split_images, embeddings_data)):
                                # Generate new filename
                                if len(split_images) == 1:
                                    new_filename = f"{original_stem}.tiff"
                                else:
                                    new_filename = f"{original_stem}_{j+1}.tiff"

                                new_file_path = original_path.parent / new_filename

                                # Save split image
                                try:
                                    image_bytes = base64.b64decode(split_image_data['image_data'])
                                    with open(new_file_path, 'wb') as f:
                                        f.write(image_bytes)
                                    new_filenames.append(new_filename)

                                    # Prepare point for Qdrant
                                    point_id = generate_uuid(original_stem if len(split_images) == 1 else f"{original_stem}_{j+1}")
                                    embedding = np.array(embedding_data['embedding'], dtype=np.float32)

                                    point = PointStruct(
                                        id=point_id,
                                        vector={"siglip_vector": embedding.tolist()},
                                        payload={
                                            "ip_type": ip_type,
                                            "reg_no": reg_no
                                        }
                                    )
                                    points_to_upsert.append(point)

                                except Exception as e:
                                    log_message(f"Error saving split image {new_filename}: {str(e)}", level='ERROR')

                            # Update database with new filenames
                            if new_filenames:
                                await self.update_patent_fig_files(reg_no, original_path.name, new_filenames)

                            # Upload embeddings to Qdrant
                            if points_to_upsert:
                                try:
                                    self.client.upsert(
                                        collection_name=self.collection_name,
                                        points=points_to_upsert,
                                        wait=True
                                    )
                                    log_message(f"Uploaded {len(points_to_upsert)} embeddings for {reg_no}", level='INFO')
                                except Exception as e:
                                    log_message(f"Error uploading embeddings for {reg_no}: {str(e)}", level='ERROR')

                            # Delete original image
                            try:
                                os.remove(original_image_path)
                                log_message(f"Deleted original image: {original_image_path}", level='DEBUG')
                            except Exception as e:
                                log_message(f"Error deleting original image {original_image_path}: {str(e)}", level='WARNING')

                        queue_size = self.queue.qsize()
                        log_message(f"Processed {len(valid_items)} images with splitting, {queue_size} items remaining in queue", level='INFO')
                    else:
                        log_message(f"Splitting API error: {result.get('message', 'Unknown error')}", level='ERROR')
        except Exception as e:
            log_message(f"Splitting processing failed: {str(e)}", level='ERROR')
    
    async def enqueue(self, ip_type, ser_no, image_path):
        """Add image to processing queue (individual enqueue - use batch_enqueue for better performance)"""
        if not ser_no:
            log_message("Skipping item with missing ser_no", level='WARNING')
            return False

        if ip_type == "Trademark":
            point_id = generate_uuid(ser_no)
        elif ip_type == "Patent":
            point_id = generate_uuid(os.path.splitext(os.path.basename(image_path))[0]) # Use filename without extension as ID for patents
        await self.queue.put((ip_type, point_id, image_path, ser_no))
        # log_message(f"Enqueued embedding for {ser_no}, queue size: {self.queue.qsize()}", level='DEBUG')
        return True

    async def batch_enqueue(self, items, batch_size=1000):
        """
        Efficiently enqueue multiple items after batch checking for existing embeddings

        Args:
            items: List of tuples (ser_no, image_path)
            batch_size: Size of batches for Qdrant existence checks

        Returns:
            tuple: (enqueued_count, skipped_count)
        """
        if not items:
            return 0, 0

        # Generate point IDs for all items
        if items[0][0] == "Trademark":
            items_with_ids = [(ip_type, generate_uuid(ser_no), image_path, ser_no) for ip_type, ser_no, image_path in items if ser_no]
        elif items[0][0] == "Patent":
            items_with_ids = [(ip_type, generate_uuid(os.path.splitext(os.path.basename(image_path))[0]), image_path, reg_no) for ip_type, reg_no, image_path in items if reg_no]

        if not items_with_ids:
            log_message("No valid items to enqueue", level='WARNING')
            return 0, 0

        all_point_ids = [item[1] for item in items_with_ids]
        existing_ids = set()

        # Batch check for existing embeddings
        log_message(f"Checking existence of {len(all_point_ids)} embeddings in batches of {batch_size}", level='INFO')

        for i in range(0, len(all_point_ids), batch_size):
            batch_ids = all_point_ids[i:i + batch_size]
            try:
                existing_points = self.client.retrieve(
                    collection_name=self.collection_name,
                    ids=batch_ids,
                    with_vectors=False
                )
                existing_ids.update(point.id for point in existing_points)
            except Exception as e:
                log_message(f"Qdrant batch check failed for batch {i//batch_size + 1}: {str(e)}", level='WARNING')
                # Continue without adding to existing_ids if check fails

        # Enqueue only items that don't already exist
        enqueued_count = 0
        skipped_count = 0

        for ip_type, point_id, image_path, num in items_with_ids:
            if point_id not in existing_ids:
                await self.queue.put((ip_type, point_id, image_path, num))
                enqueued_count += 1
            else:
                # One off update of the reg_no payload for patents: only usefull if later on I want to change the reg number for design patent to be without 0 (USPTO API format) to be with 0 (USPTO bulk format)
                # try:
                #     self.client.set_payload(
                #         collection_name=self.collection_name,
                #         payload={"reg_no": num},
                #         points=[point_id]
                #     )
                # except Exception as e:
                #     log_message(f"Failed to update reg_no for patent {num}: {str(e)}", level='WARNING')
                
                skipped_count += 1

        log_message(f"✅ Batch enqueue completed: 📈 {enqueued_count} enqueued, 📊 {skipped_count} skipped (already exist)", level='INFO')
        return enqueued_count, skipped_count

    async def batch_enqueue_for_splitting(self, items, batch_size=1000):
        """
        Efficiently enqueue multiple items for splitting workflow (no point_id generation needed)

        Args:
            items: List of tuples (ip_type, reg_no, image_path)
            batch_size: Size of batches for processing

        Returns:
            tuple: (enqueued_count, skipped_count)
        """
        if not items:
            return 0, 0

        # For splitting workflow, we don't need to check existing embeddings
        # since the point_ids will be generated after splitting
        enqueued_count = 0

        for ip_type, reg_no, image_path in items:
            if reg_no and os.path.exists(image_path):
                await self.queue.put((ip_type, reg_no, image_path))
                enqueued_count += 1
            else:
                log_message(f"Skipping invalid item: reg_no={reg_no}, path_exists={os.path.exists(image_path) if image_path else False}", level='WARNING')

        log_message(f"✅ Batch enqueue for splitting completed: 📈 {enqueued_count} enqueued", level='INFO')
        return enqueued_count, 0

    async def update_patent_fig_files(self, reg_no, original_filename, new_filenames):
        """Update the fig_files field in the patents table"""
        try:
            conn = get_db_connection()
            if not conn:
                log_message("Failed to get database connection for fig_files update", level='ERROR')
                return

            with conn.cursor() as cursor:
                # Get current fig_files array
                cursor.execute("SELECT fig_files FROM patents WHERE reg_no = %s", (reg_no,))
                result = cursor.fetchone()

                if not result:
                    log_message(f"Patent {reg_no} not found in database", level='WARNING')
                    return

                current_fig_files = result[0] or []

                # Replace original filename with new filenames
                updated_fig_files = []
                for filename in current_fig_files:
                    if filename == original_filename:
                        updated_fig_files.extend(new_filenames)
                    else:
                        updated_fig_files.append(filename)

                # Update the database
                cursor.execute(
                    "UPDATE patents SET fig_files = %s WHERE reg_no = %s",
                    (updated_fig_files, reg_no)
                )
                conn.commit()

                log_message(f"Updated fig_files for {reg_no}: replaced {original_filename} with {new_filenames}", level='INFO')

        except Exception as e:
            log_message(f"Error updating fig_files for {reg_no}: {str(e)}", level='ERROR')
        finally:
            if conn:
                conn.close()

    def get_queue_status(self):
        """Get current queue status for debugging"""
        return {
            'queue_size': self.queue.qsize(),
            'active_workers': len(self.workers),
            'shutdown_requested': self.shutdown_event.is_set()
        }
    
    async def worker(self, worker_id):
        """Process queue items in batches"""
        log_message(f"Embedding worker {worker_id} started", level='INFO')

        while not self.shutdown_event.is_set():
            batch = []
            batch_items = []

            # Collect up to BATCH_SIZE items for a batch, but don't wait too long
            try:
                # Wait for at least one item, but with timeout
                item = await asyncio.wait_for(self.queue.get(), timeout=5.0)
                batch.append(item)
                batch_items.append(item)

                # Try to get more items quickly to fill the batch
                while len(batch) < BATCH_SIZE:
                    try:
                        item = await asyncio.wait_for(self.queue.get(), timeout=0.1)
                        batch.append(item)
                        batch_items.append(item)
                    except asyncio.TimeoutError:
                        break

            except asyncio.TimeoutError:
                # No items available, check if we should continue
                if self.shutdown_event.is_set():
                    break
                continue

            if batch:
                # log_message(f"Worker {worker_id} processing batch of {len(batch)} items", level='DEBUG')
                try:
                    async with self.semaphore:
                        # Determine workflow based on item structure
                        if len(batch[0]) == 4:
                            # Old workflow: (ip_type, point_id, image_path, ser_no)
                            await self.process_batch(batch)
                        elif len(batch[0]) == 3:
                            # New splitting workflow: (ip_type, reg_no, image_path)
                            await self.process_batch_with_splitting(batch)
                        else:
                            log_message(f"Unknown batch item format: {batch[0]}", level='ERROR')
                    # log_message(f"Worker {worker_id} completed batch of {len(batch)} items", level='DEBUG')
                except Exception as e:
                    log_message(f"Worker {worker_id} batch processing failed: {str(e)}", level='ERROR')
                finally:
                    # Mark all items in this batch as done
                    for _ in batch_items:
                        self.queue.task_done()

        log_message(f"Embedding worker {worker_id} stopped", level='INFO')

    async def start(self):
        """Start multiple processing workers"""
        if self.workers:
            log_message("Workers already started", level='WARNING')
            return

        # Create and start multiple workers
        for i in range(self.num_workers):
            worker_task = asyncio.create_task(self.worker(i))
            self.workers.append(worker_task)

        log_message(f"Started {self.num_workers} embedding queue workers", level='INFO')

    async def stop(self):
        """Stop all workers gracefully"""
        if not self.workers:
            return

        log_message("Stopping embedding queue workers...", level='INFO')

        # Signal shutdown
        self.shutdown_event.set()

        # Wait for workers to finish
        await asyncio.gather(*self.workers, return_exceptions=True)

        self.workers.clear()
        log_message("All embedding queue workers stopped", level='INFO')