from langfuse import observe
import langfuse
import time
import asyncio

from CheckLite.RAG.qdrant_search import find_similar_assets_qdrant

@observe(capture_input=False, capture_output=False)
async def check_patents(client, bucket, temp_dir, client_id, check_id, local_product_images, description, ip_keywords, cases_df, plaintiff_df, precomputed_embeddings_map=None, language='zh'):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "check_id": check_id,
        "local_product_images": local_product_images,
        "description": description, "ip_keywords": ip_keywords, "language": language
    })
    start_time = time.time()

    sim_results = await find_similar_assets_qdrant(
        query_image_paths=local_product_images,
        check_id=check_id,
        client_id=client_id,
        ip_type="Patent",
        temp_dir=temp_dir,
        cases_df=cases_df,
        plaintiff_df=plaintiff_df,
        plaintiff_id=None,
        top_n=3,
        similarity_threshold=0.6,
        similarity_threshold_text=0.25,
        precomputed_embeddings_map=precomputed_embeddings_map
    )

    print(f"\033[94mPatent: Patent RAG for {len(local_product_images)} pictures done in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        print(f"\033[94m ✅ Patent: RAG returned {len(sim_results)} potential patent matches in {time.time() - start_time:.1f} seconds\033[0m")
        langfuse.get_client().update_current_span(output={"results": sim_results})
        return sim_results
    else:
        print(f"\033[94m ✅ Patent: RAG returned no pottential patent matches in {time.time() - start_time:.1f} seconds\033[0m")
        langfuse.get_client().update_current_span(output={"results": []})
        return []


if __name__ == '__main__':
    # This block allows the script to be run directly for testing purposes.

    import tempfile
    import os
    import sys

    # Ensure the project root is in the Python path to resolve imports correctly.
    sys.path.append(os.getcwd())

    from DatabaseManagement.ImportExport import get_table_from_GZ
    from CheckLite.RAG.qdrant_search import get_siglip_embeddings

    async def main():
        """Main function to run a standalone patent check on a single local image."""
        print("--- Running Standalone Patent Check ---")
        
        # HARDCODED: Provide a path to a local image for testing.
        test_image_path = r"D:\Win10User\Downloads\CoffeeCompany\product_1_1d311b28-7078-4e31-9176-54797e058ddf.jpg"
        local_product_images = [test_image_path]
        
        # Create a persistent directory for output, named after the test file.
        output_dir = os.path.join(os.path.dirname(test_image_path), os.path.splitext(os.path.basename(test_image_path))[0] + "_output")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Using output directory: {output_dir}")

        cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
        
        embedding = await get_siglip_embeddings(local_product_images)
        precomputed_embeddings_map = {path: emb for path, emb in zip(local_product_images, embedding)}
        

        print(f"Checking file: {local_product_images[0]}")
        results = await check_patents(
            client=None,
            bucket=None,
            temp_dir=output_dir, # Pass the persistent output directory
            client_id="local_test",
            check_id="local_test",
            local_product_images=local_product_images,
            description="",
            ip_keywords=[],
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            precomputed_embeddings_map=precomputed_embeddings_map
        )
        print("\n--- Patent Check Results ---")
        print(results)
        print("--- End of Report ---")

    asyncio.run(main())
