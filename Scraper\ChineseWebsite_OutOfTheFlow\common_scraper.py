import os
import re
import sys
import time
import logging
import aiohttp

import pandas as pd
import requests
from bs4 import BeautifulSoup
from dateutil.parser import parse
from pytz import timezone
from markdownify import markdownify

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from Common.Constants import sanitize_name
from IP.Trademarks_Bulk.trademark_db import get_db_connection, get_table_from_db
from DatabaseManagement.ImportExport import get_table_from_GZ
from Scraper.ChineseWebsite_OutOfTheFlow.single_article import B_extract_data_from_url
from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number, get_info_from_USCO_using_reg_no
from Alerts.Chrome_Driver import get_driver
from Scraper.ChineseWebsites_utils import fetch_url
from Alerts.Single_Docket import process_single_docket

TABLE_NAME = "cn_websites"
CHINA_TZ = timezone('Asia/Shanghai')

def setup_logger(log_file):
    """Set up a logger for the scraper."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def get_html_content(url: str, retries=3, delay=3) -> str | None:
    """
    Fetches the HTML content of a given URL with retries.
    """
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=20)
            if response.status_code == 404:
                logging.warning(f"URL {url} returned a 404 Not Found status.")
            else:
                response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logging.warning(f"Request for {url} failed (attempt {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                logging.error(f"Failed to get HTML content for {url} after {retries} attempts.")
                return None

def _format_date_for_db(date_str: str) -> str | None:
    """
    Parses a date string and formats it for PostgreSQL.
    """
    if not date_str or not isinstance(date_str, str):
        return None
    try:
        if '-' in date_str and len(date_str) > 10:
            date_str = date_str.split('-')[0]
        dt = parse(date_str)
        return dt.strftime('%Y-%m-%d')
    except (ValueError, TypeError, IndexError):
        logging.warning(f"Could not parse date string: {date_str}")
        return None

def create_table_if_not_exists(conn):
    """
    Creates the 'cn_websites' and 'cn_websites_files' tables if they do not already exist.
    """
    with conn.cursor() as cur:
        cur.execute(f"""
            CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                id SERIAL PRIMARY KEY,
                posting_date DATE,
                docket_in_title TEXT,
                docket_formated TEXT,
                case_id INTEGER,
                views INTEGER,
                url TEXT,
                case_number_in_content TEXT[],
                trademarks_reg_nos TEXT[],
                copyright_reg_nos TEXT[],
                patent_reg_nos TEXT[],
                artist_url TEXT,
                source_website TEXT,
                scraped_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS cn_websites_files (
                id SERIAL PRIMARY KEY,
                filename TEXT,
                cn_websites_id INTEGER REFERENCES cn_websites(id),
                type TEXT,
                reg_no TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        conn.commit()
        logging.info(f"Tables '{TABLE_NAME}' and 'cn_websites_files' are ready.")

async def _extract_dockets_and_plaintiffs(content, case_df, plaintiff_df, logger, title_docket):
    """
    Extract all dockets from article content and map to plaintiffs.
    Returns a dictionary mapping plaintiff names to lists of dockets.
    """
    # Extract all dockets from the content using regex
    soup = BeautifulSoup(content, 'html.parser')
    selected_element = soup.select_one(".entry-content")
    html_to_convert = str(selected_element)
    markdown_content = markdownify(html_to_convert, heading_style="ATX", bullets='*', body_width=0)
    
    all_dockets = re.findall(r'\d{2,}-cv-\d{2,}', markdown_content, re.IGNORECASE)
    
    if title_docket:
        all_dockets.append(title_docket)
    
    all_dockets = list(set(all_dockets))
    
    # If no dockets found, return empty dict
    if not all_dockets:
        logger.warning("No dockets found in article content.")
        return {}
        
    all_search_dockets = []
    for docket in all_dockets:
        year_match = re.search(r'(\d{2,4})-cv-(\d+)', docket, re.IGNORECASE)
        if not year_match:
            continue
            
        year = year_match.group(1)[-2:]
        number = year_match.group(2).zfill(5)
        search_docket = f"{year}-cv-{number}"
        all_search_dockets.append(search_docket)
    
    all_search_dockets = list(set(all_search_dockets))
        
    # Map dockets to plaintiff_names
    # {plaintiff_name: [docket, ...]}
    plaintiff_data = {}    
    for search_docket in all_search_dockets:
        matching_cases = case_df[case_df['docket'].str.contains(search_docket, na=False)]
        matching_copyright_cases = matching_cases[matching_cases['nos_description'] == 'Copyrights']
        if len(matching_cases)== 1 or len(matching_copyright_cases) == 1:
            if len(matching_cases) == 1:
                case_row = matching_cases.iloc[0]
            else:
                case_row = matching_copyright_cases.iloc[0]
                
            # Use the first matching case if multiple found
            plaintiff_id = int(case_row.get('plaintiff_id'))
            if plaintiff_id:
                case_id = int(case_row.get('id'))
                plaintiff_name = plaintiff_df[plaintiff_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
                docket_found = case_row.get("docket")
                if plaintiff_name not in plaintiff_data:
                    plaintiff_data[plaintiff_name] = []
                if (docket_found, case_id) not in plaintiff_data[plaintiff_name]:
                    plaintiff_data[plaintiff_name].append((docket_found, case_id))
        elif len(matching_cases) > 1:
            # Placeholder for more complex overlap logic
            logger.error(f"Multiple copyright cases found for {search_docket}. Placeholder needed.")
        else: # No matching cases found, attempt to fetch new case after retrieving the article content
            logger.warning(f"No case found for {search_docket}. Attempting to fetch from Lexus")
            try:
                clean_markdown = re.sub(r'!\[.*?\]\(.*?\)', '', markdown_content, flags=re.DOTALL)
                new_case_df, updated_case_df, plaintiff_df = await process_single_docket(
                    docket_number=search_docket,
                    article_content=clean_markdown,  # Use extracted article text
                    db_case_df=case_df,
                    plaintiff_df=plaintiff_df
                )
                if updated_case_df is not None:
                    case_df = updated_case_df
                if new_case_df is not None and not new_case_df.empty:
                    # Ensure the 'docket' column exists and is not empty
                    if 'docket' in new_case_df.columns and not new_case_df['docket'].isnull().all():
                        case_id = int(new_case_df.iloc[0]['id'])
                        case_row = case_df[case_df['id'] == case_id]
                        docket_found = case_row['docket'].values[0]
                        plaintiff_id = int(case_row['plaintiff_id'].values[0])
                        if plaintiff_id:
                            plaintiff_name = plaintiff_df[plaintiff_df['id'] == plaintiff_id]['plaintiff_name'].values[0]
                            if plaintiff_name not in plaintiff_data:
                                plaintiff_data[plaintiff_name] = []
                            if (search_docket, case_row.get("id")) not in plaintiff_data[plaintiff_name]:
                                plaintiff_data[plaintiff_name].append((docket_found, case_id))

            except Exception as e:
                logger.error(f"Failed to fetch case {search_docket}: {e}")
    
    return plaintiff_data, case_df, plaintiff_df

async def process_article(session, conn, article, case_df, plaintiff_df, copyrights_df, config):
    """
    Process a single article: fetch details from its URL and insert all data into the database.
    """
    logger = config['logger']
    url = article.get('url')
    if not url:
        logger.warning("Skipping article with no URL.")
        return case_df, plaintiff_df,0, 0

    logger.info(f"Processing article: {url}")

    # Fetch the content of the article
    content = await fetch_url(session, url)
    if not content:
        logger.error(f"Could not fetch article content for {url}")
        return case_df, plaintiff_df,0, 0

    docket_in_title = article.get('docket_in_title')
    
    soup = BeautifulSoup(content, 'html.parser')
    views_element = soup.select_one('.post-views-count')
    article['views'] = int(views_element.get_text(strip=True)) if views_element else 0
                        
    # Extract dockets and map to plaintiffs using helper function
    plaintiff_data, case_df, plaintiff_df = await _extract_dockets_and_plaintiffs(content, case_df, plaintiff_df, logger, docket_in_title)
                        
    # If no valid dockets found, skip
    if not plaintiff_data:
        logger.warning(f"No valid dockets found for article {url}. Skipping.")
        return case_df, plaintiff_df,0, 0
                            
    # Process each plaintiff separately
    total_copyrights_count = 0
    total_files_count = 0
                        
    for plaintiff_name, cases in plaintiff_data.items():
        # Process each plaintiff-docket pair

        # Use the first docket for this plaintiff for folder naming
        first_docket, case_id = cases[0]
        case_folder = sanitize_name(first_docket) if first_docket else "unknown"
        copyright_cn_allpictures_dir = os.path.join(config['image_dir'], case_folder)
        os.makedirs(copyright_cn_allpictures_dir, exist_ok=True)
        
        # Run B_extract_data_from_url for this plaintiff
        json_response, downloaded_files, article_text = await B_extract_data_from_url(session, url, content, config['site_name'].lower(), first_docket or "Unknown", plaintiff_name, copyright_cn_allpictures_dir)
        
        if not json_response:
            logger.error(f"Failed to extract details for {url} (plaintiff {plaintiff_name}). Skipping.")
            return case_df, plaintiff_df, 0, 0
            
        # Process copyrights for this plaintiff
        driver = None
        processed_copyrights = []
        new_copyrights_count = 0
        try:
            for copy_info in json_response.get("copyrights", []):
                reg_no = copy_info.get('reg_no')
                if not reg_no or "multi" in reg_no or "no_reg" in reg_no:
                    continue
                formatted_reg_no = await extract_formated_copyright_registration_number(reg_no)
                if formatted_reg_no and not copyrights_df[copyrights_df['registration_number'] == formatted_reg_no].empty:
                    processed_copyrights.append(formatted_reg_no)
                    continue
                if not formatted_reg_no:
                    continue
                if not driver:
                    driver = get_driver()
                logger.info(f"Fetching USCO info for {formatted_reg_no}")
                usco_info = await get_info_from_USCO_using_reg_no(formatted_reg_no, driver)
                if usco_info:
                    # Find the plaintiff_id for this plaintiff_name
                    plaintiff_row = plaintiff_df[plaintiff_df['plaintiff_name'] == plaintiff_name]
                    plaintiff_id = None
                    if not plaintiff_row.empty:
                        plaintiff_id = int(plaintiff_row.iloc[0].get('id'))
                        
                    with get_db_connection() as conn_inner:
                        with conn_inner.cursor() as cur:
                            cur.execute(
                                """
                                INSERT INTO copyrights (tro, registration_number, registration_date, type_of_work, title, date_of_creation, date_of_publication, copyright_claimant, authorship_on_application, rights_and_permissions, description, nation_of_first_publication, names, certificate_status, plaintiff_id)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                ON CONFLICT (registration_number) DO NOTHING
                                """,
                                (
                                    True, usco_info.get("registration_number"), _format_date_for_db(usco_info.get("registration_date")),
                                    usco_info.get("type_of_work"), usco_info.get("title"), usco_info.get("date_of_creation"),
                                    _format_date_for_db(usco_info.get("date_of_publication")), usco_info.get("copyright_claimant"),
                                    usco_info.get("authorship_on_application"), usco_info.get("rights_and_permissions"),
                                    usco_info.get("description"), usco_info.get("nation_of_first_publication"),
                                    usco_info.get("names"), "Missing", plaintiff_id,
                                )
                            )
                    logger.info(f"Inserted a new copyright into database")
                    new_copyrights_count += 1
                    processed_copyrights.append(usco_info.get("registration_number"))
        finally:
            if driver:
                driver.quit()
                
        # Insert into cn_websites table for this plaintiff
        with conn.cursor() as cur:
            cur.execute(
                f"""
                INSERT INTO {TABLE_NAME} (
                    posting_date, docket_in_title, docket_formated, case_id, views, url,
                    case_number_in_content, trademarks_reg_nos, copyright_reg_nos,
                    patent_reg_nos, artist_url, source_website
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """,
                (
                    article.get('posting_date'), docket_in_title, first_docket, 
                    case_id, article.get('views', 0), 
                    url, json_response.get("case_numbers"), json_response.get("trademarks"),
                    processed_copyrights, json_response.get("patents"), json_response.get("artist_url"),
                    config['site_name']
                )
            )
            new_article_id = cur.fetchone()[0]
            conn.commit()

        logger.info(f"Successfully processed and inserted article: {url} for plaintiff {plaintiff_name} with ID: {new_article_id}")

        # Insert file records
        copyrights_info = {item['identifier']: item['reg_no'] for item in json_response.get("copyrights", []) if item.get('identifier')}
        with conn.cursor() as cur:
            for identifier, (filepath, _) in downloaded_files.items():
                reg_no_raw = copyrights_info.get(identifier)
                reg_no_formatted = None
                if reg_no_raw:
                    if reg_no_raw.lower() == 'multi':
                        file_type = 'multi'
                    else:
                        file_type = 'single'
                        reg_no_formatted = await extract_formated_copyright_registration_number(reg_no_raw)
                else:
                    file_type = 'single'
                cur.execute(
                    """
                    INSERT INTO cn_websites_files (filename, cn_websites_id, type, reg_no)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (os.path.basename(filepath), new_article_id, file_type, reg_no_formatted)
                )
            conn.commit()
        logger.info(f"Inserted {len(downloaded_files)} file records for article ID {new_article_id}")
                            
        total_copyrights_count += new_copyrights_count
        total_files_count += len(downloaded_files) 
                
    return case_df, plaintiff_df, total_copyrights_count, total_files_count

async def run_scraper(config):
    """Main async function to run the scraper."""
    logger = config['logger']
    logger.info(f"Starting {config['site_name']} scraper.")

    conn = get_db_connection()
    if conn is None:
        logger.error("Could not establish a database connection. Exiting.")
        return {}

    create_table_if_not_exists(conn)

    try:
        case_df = get_table_from_GZ("tb_case", force_refresh=True)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)
        copyrights_df = get_table_from_db("copyrights")
        existing_df = get_table_from_db(TABLE_NAME)
        logger.info(f"Loaded {len(existing_df)} existing records from '{TABLE_NAME}'.")
    except Exception as e:
        logger.error(f"Failed to load existing data: {e}")
        existing_df = pd.DataFrame()

    stop_scraping = False
    page_num = 1
    new_articles_count, new_copyrights_count, new_files_count = 0, 0, 0
    processed_dockets = []

    async with aiohttp.ClientSession() as session:
        while not stop_scraping:
            url = config['base_url'].format(page_num)
            logger.info(f"Scraping page {page_num}: {url}")

            html_content = get_html_content(url)
            if not html_content or config['last_page_check'](html_content):
                if not html_content:
                    page_num +=1
                    continue
                logger.info("Reached the last page.")
                break

            articles = config['parse_html_func'](html_content)
            if not articles:
                logger.warning(f"No articles found on page {page_num}.")
                page_num += 1
                continue

            for article in articles:
                is_existing = False
                if not existing_df.empty and 'url' in existing_df.columns and article.get('url'):
                    if (existing_df['url'] == article['url']).any():
                        is_existing = True
                
                if is_existing and config['site_name'] == "Maijiazhichi":
                    logger.info(f"Found an existing article ({article.get('url')}). Stopping scraper.")
                    stop_scraping = True
                    break
                elif is_existing and config['site_name'] == "SellerDefense":
                     logger.info(f"Found an existing article ({article.get('url')}). Checking if it's a sticky post.")
                else:
                    case_df, plaintiff_df, copyrights_added, files_added  = await process_article(session, conn, article, case_df, plaintiff_df, copyrights_df, config)
                    new_articles_count += 1
                    new_copyrights_count += copyrights_added
                    new_files_count += files_added
                    existing_df = pd.concat([existing_df, pd.DataFrame([article])], ignore_index=True)

            if stop_scraping:
                break
            page_num += 1

    stats = {
       "new_articles_inserted": new_articles_count,
       "new_copyrights_fetched": new_copyrights_count,
       "new_files_added": new_files_count,
    }
    logger.info(f"Scraper finished. Stats: {stats}")
    conn.close()
    return stats