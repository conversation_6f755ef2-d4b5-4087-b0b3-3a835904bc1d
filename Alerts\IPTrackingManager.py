from typing import Dict, Any, Op<PERSON>, Tu<PERSON>, Set, List
# Add import for cleaning patent numbers
import pandas as pd # Added import for pandas
import copy
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_id
from langfuse import observe
import langfuse
from Alerts.PicturesProcessing.CopyrightDatabaseUtils import CopyrightDatabaseManager,generate_copyright_filename,standardize_reg_no,handle_copyright_file_update, get_method_priority
from IP.Trademarks_Bulk.trademark_db import get_table_from_db


class IPTrackingManager:
    """Manages the state and logic for tracking IP evidence findings."""

    def __init__(self, nos_description_text: str, plaintiff_id: int, initial_state: Optional[Dict] = None):
        """
        Initializes the IP tracking state based on the case description or from a saved state.

        Args:
            nos_description_text: The description string from the case data.
            initial_state: Optional dictionary to initialize the state from.
        """
        self.iptypes = ['trademark', 'copyright', 'patent']
        
        # Initialize copyright dataframe to mirror PostgreSQL copyrights table
        self.plaintiff_id = int(plaintiff_id)
        try:
            # Load copyrights table using get_table_from_db
            # copyrights_df = get_table_from_db("copyrights", where_clause=f"plaintiff_id = {self.plaintiff_id} AND deleted = false")
            copyrights_df = get_table_from_db("copyrights") # Because the copyright number might be under anothe rplaintiff id due to plaintiff duplicates


            if not copyrights_df.empty:
                # Load copyrights_files table for join
                files_df = get_table_from_db("copyrights_files")

                # Join to get image_found and image_source information
                if not files_df.empty:
                    # Get best file for each registration number (lowest method priority = best)
                    files_df['method_priority'] = files_df['method'].apply(get_method_priority)
                    best_files = files_df.loc[files_df.groupby('registration_number')['method_priority'].idxmin()]

                    # Merge with copyrights
                    self.copyright_dataframe = copyrights_df.merge(best_files[['registration_number', 'method']],on='registration_number',how='left')

                    # Set image_found and image_source
                    self.copyright_dataframe['image_found'] = self.copyright_dataframe['method'].notna().astype(int)
                    self.copyright_dataframe['image_source'] = self.copyright_dataframe['method']
                    self.copyright_dataframe.drop('method', axis=1, inplace=True)
                else:
                    self.copyright_dataframe = copyrights_df.copy()
                    self.copyright_dataframe['image_found'] = 0
                    self.copyright_dataframe['image_source'] = None

                # Add certificate_found column for compatibility
                self.copyright_dataframe['certificate_found'] = 0
            else:
                # Create empty dataframe with correct columns
                self.copyright_dataframe = pd.DataFrame(columns=[
                    'id', 'tro', 'registration_number', 'registration_date', 'type_of_work',
                    'title', 'date_of_creation', 'date_of_publication', 'copyright_claimant',
                    'authorship_on_application', 'rights_and_permissions', 'description',
                    'nation_of_first_publication', 'names', 'plaintiff_id', 'create_time',
                    'update_time', 'image_found', 'image_source', 'certificate_found'
                ])
        except Exception as e:
            print(f"Error loading copyright dataframe from PostgreSQL: {e}")
            self.copyright_dataframe = pd.DataFrame(columns=[
                'id', 'tro', 'registration_number', 'registration_date', 'type_of_work',
                'title', 'date_of_creation', 'date_of_publication', 'copyright_claimant',
                'authorship_on_application', 'rights_and_permissions', 'description',
                'nation_of_first_publication', 'names', 'plaintiff_id', 'create_time',
                'update_time', 'image_found', 'image_source', 'certificate_found'
            ])
            
        # Initialize fresh state
        self._state: Dict[str, Any] = { "trademark": {}, "copyright": {}, "patent": {} }
        self._initialize_state(nos_description_text)
        
        if initial_state:
            # Restore state from initial_state, which is a product of export_state()
            for ip_type, saved_ip_state in initial_state.items():
                if ip_type in self._state:
                    state_entry = self._state[ip_type]
                    
                    # Overwrite the initialized state with the saved state
                    state_entry.update(saved_ip_state)
                    
                    # Convert restored lists back to sets
                    for key in ['target_reg_nos', 'found_reg_nos', 'non_target_reg_nos']:
                        if key in state_entry and isinstance(state_entry[key], list):
                            state_entry[key] = set(state_entry[key])

            print(f"IPTrackingManager initialized from saved state.")

        langfuse.get_client().update_current_span(
            output={"IPManagerStateInitialized": self._state}
        )

    def _initialize_state(self, case_nos_description: str):
        """Helper method to initialize the state for each IP type."""
        nos_desc_lower = case_nos_description.lower() if case_nos_description else ""

        for ip_type in self.iptypes:
            is_relevant = ip_type in nos_desc_lower

            self._state[ip_type] = {
                "is_relevant": is_relevant,
                "goal_met": not is_relevant,  # Goal is met if not relevant
                "target_reg_nos": set(), # For VA copyrights, or all for TM/PT
                "non_target_reg_nos": set(), # For non-VA copyrights
                "found_reg_nos": set(),
                "reg_nos_status": "pending", # pending, found, not_found
                "target_main_doc": False,
                "target_attachment_indices": [],
                "target_step_nbs": []
            }

    @observe()
    def update_values_from_llm(self, llm_analysis_result: dict):
        """
        Updates state values based on LLM analysis results for each IP type.
        Applies normalization to patent registration numbers. # <-- Added note

        This includes updating the 'is_relevant' flag based on 'owned_by_plaintiff',
        adding to the set of 'target_reg_nos', and replacing other target fields.

        Args:
            llm_analysis_result: The structured dictionary from LLM analysis.
                                 Expected keys: 'trademark', 'copyright', 'patent'.
                                 Each sub-dict can contain 'owned_by_plaintiff', 'target_reg_nos',
                                 'reg_nos_status', 'target_attachment_indices', 'target_step_nbs'.
        """
        # Handle None result from LLM analysis
        if llm_analysis_result is None:
            print("Warning: llm_analysis_result is None in update_values_from_llm(). Skipping update.")
            return

        for ip_type, data in llm_analysis_result.items():
            if ip_type in self._state: # Process if the IP type exists in our state
                state_entry = self._state[ip_type]

                # Update relevance based on LLM output
                llm_is_relevant = data.get("owned_by_plaintiff", False)
                if llm_is_relevant:
                    state_entry["is_relevant"] = True # Only set to True, never False

                # Update target registration numbers using set union
                llm_reg_nos = data.get("registration_numbers", [])
                if ip_type == "copyright":
                    for rn in llm_reg_nos:
                        if rn not in self.copyright_dataframe['registration_number'].tolist():
                            self.add_copyright_to_dataframe({"registration_number": rn})
                        # Add to appropriate target/non-target set
                        if rn.startswith("VA"):
                            state_entry["target_reg_nos"].add(rn)
                        else:
                            state_entry["non_target_reg_nos"].add(rn)
                else:
                    state_entry["target_reg_nos"].update(llm_reg_nos)


                # Update other fields (replace existing values)
                state_entry["reg_nos_status"] = data.get("registration_numbers_status", state_entry["reg_nos_status"])
                state_entry["target_main_doc"] = data.get("target_main_doc", state_entry["target_main_doc"])
                state_entry["target_attachment_indices"] = data.get("target_attachment_indices", [])  # already sorted and removed dupes in IPAnalysisLLM
                state_entry["target_step_nbs"] = data.get("target_step_nbs", []) # already sorted and removed dupes in IPAnalysisLLM

                # Re-evaluate goal status if relevance or targets might have changed
                self._set_goal_status(ip_type)


    def record_finding(self, ip_type: str, location_id: str, found_reg_nos: Optional[List[str]] = None):
        """
        Records findings for a specific IP type at a given location.

        Args:
            ip_type: The type of IP ('trademark', 'copyright', 'patent').
            location_id: Identifier for the location processed (e.g., "att_1", "step_5").
            found_reg_nos: A list of registration numbers found at this location.
        """
        
        if not self._state[ip_type]["is_relevant"]:
            self._state[ip_type]["is_relevant"] = True

        state_entry = self._state[ip_type]

        if found_reg_nos:
            state_entry["found_reg_nos"].update(found_reg_nos)

        self._set_goal_status(ip_type)
        
        langfuse.get_client().update_current_span(
            output={
                "Target reg nos": len(state_entry["target_reg_nos"]),
                "Found reg nos": len(state_entry["found_reg_nos"]),
                "Missing reg nos": len(state_entry["target_reg_nos"] - state_entry["found_reg_nos"]),
                "Goal met": state_entry['goal_met']
            }
        )


    def _set_goal_status(self, ip_type: str):
        """
        Checks if the goal for the specified IP type has been met based on findings.
        Updates the 'goal_met' flag in the state.
        """

        state_entry = self._state[ip_type] # type: ignore

        if ip_type == "copyright":
            if state_entry["target_reg_nos"]: # target_reg_nos for copyright now only contains "VA" numbers
                if len(state_entry["found_reg_nos"]) >= len(state_entry["target_reg_nos"]) * 0.8:
                    state_entry["goal_met"] = True
                else:
                    state_entry["goal_met"] = False
            elif state_entry["non_target_reg_nos"]: # No "VA" numbers were targeted, but copyright is relevant because of non_target_reg_nos which have been identified => we are done
                state_entry["goal_met"] = True
            elif state_entry["is_relevant"]: # Relevant, but no specific targets (VA or non-VA) from LLM
                # Goal met if *any* copyright is found (e.g. through general exhibit scan)
                state_entry["goal_met"] = bool(state_entry["found_reg_nos"])
        else: # For Patent and Trademark
            if state_entry["target_reg_nos"]:
                all_targets_found = state_entry["target_reg_nos"].issubset(state_entry["found_reg_nos"])
                state_entry["goal_met"] = all_targets_found
            else:  # if IP is relevant, but there are no target_reg_nos, then all we need is to find any IP for the goal to be met
                any_ip_found = bool(state_entry["found_reg_nos"])
                state_entry["goal_met"] = any_ip_found

    @observe()
    def update_copyright_image_found(self, registration_number: str, image_source: str, filename: str = None):
        """
        Update the copyright dataframe when an image is found and handle copyrights_files table.

        Args:
            registration_number: The copyright registration number
            image_source: The source/method where the image was found (e.g., 'Exhibit', 'TinEye', 'GoogleVision', 'GenAI')
            filename: Optional filename to use, if not provided will be generated
        """
        # Standardize registration number
        standardized_reg_no = standardize_reg_no(registration_number)

        # Generate filename if not provided
        if not filename:
            filename = generate_copyright_filename(standardized_reg_no, image_source)

        # Handle file update based on method hierarchy
        success, final_filename = handle_copyright_file_update(standardized_reg_no, filename, image_source, production=False)

        if success:
            # Update dataframe
            if not self.copyright_dataframe.empty:
                mask = self.copyright_dataframe['registration_number'] == standardized_reg_no
                if mask.any():
                    self.copyright_dataframe.loc[mask, 'image_found'] = 1
                    self.copyright_dataframe.loc[mask, 'image_source'] = image_source

        return success, final_filename

    
    @observe()
    def get_copyrights_without_images(self) -> pd.DataFrame:
        """
        Get copyrights from the dataframe where image_found is False or NULL.

        Returns:
            DataFrame of copyrights without images found
        """
        if self.copyright_dataframe.empty:
            return pd.DataFrame()

        # Filter for records where image_found is False, NULL, or 0
        mask = (self.copyright_dataframe['image_found'].isna()) | (self.copyright_dataframe['image_found'] == 0)
        return self.copyright_dataframe[mask].copy()

    
    @observe()
    def add_copyright_to_dataframe(self, copyright_data: dict, location_id: str = None):
        """
        Add a new copyright record to the dataframe and PostgreSQL copyrights table.

        Args:
            copyright_data: Dictionary containing copyright information
            location_id: Optional location identifier for tracking
        """
        # Ensure plaintiff_id is there
        if not copyright_data or not isinstance(copyright_data, dict):
            print("Warning: copyright_data is empty or not a dictionary. Skipping add.")
            return

        if 'plaintiff_id' not in copyright_data or copyright_data['plaintiff_id'] is None:
            copyright_data['plaintiff_id'] = self.plaintiff_id

        # Standardize registration number if present
        if 'registration_number' in copyright_data:
            copyright_data['registration_number'] = standardize_reg_no(copyright_data['registration_number'])

        # Set default values for required fields
        copyright_data.setdefault('tro', True)  # Use lowercase 'tro' for PostgreSQL
        copyright_data.setdefault('image_found', 0)
        copyright_data.setdefault('image_source', None)
        copyright_data.setdefault('certificate_found', 0)

        # Upsert to PostgreSQL copyrights table
        try:
            with CopyrightDatabaseManager() as db_manager:
                cursor = db_manager.db_conn.cursor()

                # Use copyright_data directly, just remove None values and non-DB fields
                pg_data = {k: v for k, v in copyright_data.items()
                          if v is not None and k not in ['image_found', 'image_source', 'certificate_found']}

                # Build upsert query
                columns = list(pg_data.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                update_set = ', '.join([f'"{col}" = EXCLUDED."{col}"' for col in columns if col != 'registration_number'])
                update_set += ', update_time = NOW()'

                query = f"""
                    INSERT INTO copyrights ({', '.join([f'"{col}"' for col in columns])})
                    VALUES ({placeholders})
                    ON CONFLICT (registration_number) DO UPDATE SET
                        {update_set}
                """

                cursor.execute(query, list(pg_data.values()))
                db_manager.db_conn.commit()
                cursor.close()

        except Exception as e:
            print(f"Error upserting copyright to PostgreSQL: {e}")

        # Update local dataframe
        new_row = pd.DataFrame([copyright_data])
        if self.copyright_dataframe.empty:
            self.copyright_dataframe = pd.concat([self.copyright_dataframe, new_row], ignore_index=True)
        else:
            existing_mask = self.copyright_dataframe['registration_number'] == new_row['registration_number'].iloc[0]
            if existing_mask.any():
                # Update existing record
                for key, value in copyright_data.items():
                    self.copyright_dataframe.loc[existing_mask, key] = value
            else:
                # Append new record if it doesn't exist
                self.copyright_dataframe = pd.concat([self.copyright_dataframe, new_row], ignore_index=True)

    
    
    @observe()
    def generate_md_registration_number(self) -> str:
        """
        Generate a new MD registration number for unknown copyrights.
        Format: MDxxxxyyyy where xxxx is plaintiff_id 0-padded to 4 digits, yyyy is consecutive number.

        Args:
            plaintiff_id: The plaintiff ID

        Returns:
            Generated registration number
        """
        # Get existing MD numbers for this plaintiff
        plaintiff_str = f"{self.plaintiff_id:04d}"
        pattern = f"MD{plaintiff_str}"

        existing_numbers = []
        if not self.copyright_dataframe.empty:
            mask = self.copyright_dataframe['registration_number'].str.startswith(pattern, na=False)
            existing_md_numbers = self.copyright_dataframe[mask]['registration_number'].tolist()

            # Extract the consecutive numbers
            for reg_no in existing_md_numbers:
                if len(reg_no) == 10 and reg_no.startswith(pattern):  # MD + 4 digits + 4 digits
                    try:
                        consecutive_num = int(reg_no[-4:])
                        existing_numbers.append(consecutive_num)
                    except ValueError:
                        continue

        # Find next consecutive number
        next_num = 1
        if existing_numbers:
            next_num = max(existing_numbers) + 1

        return f"MD{plaintiff_str}{next_num:04d}"



    def is_goal_met(self, ip_type: str) -> bool:
        return self._state.get(ip_type, {}).get("goal_met", True) # Default to True if type invalid/not relevant

    def is_goal_relevant(self, ip_type: str) -> bool:
        return self._state.get(ip_type, {}).get("is_relevant", False)

    def are_all_relevant_goals_met(self) -> bool:
        """
        Checks if the goals for all relevant IP types have been met.

        Returns:
            True if all relevant goals are met, False otherwise.
        """
        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                return False
        return True
    
    def are_all_step_processing_goals_met(self) -> bool:
        """
        Checks if goals that require step-by-step document processing (like exhibits) are met.
        For Copyright, this means some  'target_reg_nos' (VA numbers from LLM) have been identified and
        either fully processed (artwork found in `found_reg_nos`) or their certificate was found.
        For TM/PT, this is the same as `are_all_relevant_goals_met` if only considering `found_reg_nos`.
        """
        for ip_type, state_entry in self._state.items(): # type: ignore
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                if ip_type == 'copyright':
                    # We stop the step processing as soon as we found the registration number => the image must be in the same steps. This is restrictive.
                    reg_no_with_certificate_found = set(self.copyright_dataframe[self.copyright_dataframe['certificate_found']==1]['registration_number'].tolist())
                    if len(state_entry["target_reg_nos"]) == 0 and state_entry["target_reg_nos"].issubset(state_entry["found_reg_nos"] | reg_no_with_certificate_found):
                        return False # Copyright step processing still needed for some VA numbers
                else: # For TM/PT, if goal_met is false (based on found_reg_nos), step processing is not done.
                    return False
        return True

    def get_missing_status(self) -> str:
        """
        Returns a sentence explaining what is still missing for unmet goals.
        """
        missing_statuses = []
        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                if "target_reg_nos" in state_entry and state_entry["target_reg_nos"]:
                    missing_reg_nos = state_entry["target_reg_nos"] - state_entry.get("found_reg_nos", set())
                    if missing_reg_nos:
                        missing_statuses.append(f"Looking for {ip_type.capitalize()} Reg No: {', '.join(sorted(list(missing_reg_nos)))}.")
                else:
                    missing_statuses.append(f"Looking for unknown {ip_type.capitalize()} reg numbers.")

        if not missing_statuses:
            return "Nothing Missing." # Return empty string if all relevant goals are met

        # Join statuses into a nice sentence
        return " ".join(missing_statuses)

    def get_targeted_locations_for_unmet_goals(self) -> Tuple[Set[int], Set[int]]:
        """
        Gets the set of targeted attachment indices and step numbers for IP types
        whose goals are not yet met.

        Returns:
            A tuple containing two sets: (attachment_indices, step_numbers).
        """
        unmet_attachment_indices: Set[int] = set()
        unmet_step_nbs: Set[int] = set()

        for ip_type, state_entry in self._state.items():
            if isinstance(state_entry, dict) and state_entry.get("is_relevant", False) and not state_entry.get("goal_met", False):
                unmet_attachment_indices.update(state_entry.get("target_attachment_indices", set()))
                unmet_step_nbs.update(state_entry.get("target_step_nbs", set()))

        return sorted(list(unmet_attachment_indices)), sorted(list(unmet_step_nbs))

    def export_state(self) -> Dict[str, Any]:
        """
        Returns an optimized, serializable copy of the internal state dictionary.
        Removes empty collections and unnecessary fields to minimize JSON size.
        Converts sets to sorted lists for JSON compatibility.

        Returns:
            A dictionary representing the minimal tracking state, ready for serialization.
        """
        # Create a new state dictionary with only essential information
        optimized_state = {}

        # Process each IP type
        for ip_type in self.iptypes:
            if ip_type in self._state:
                ip_type_state = self._state[ip_type]

                # Skip IP types that aren't relevant
                if not ip_type_state.get('is_relevant', False):
                    continue

                # Start with essential fields
                optimized_ip_state = {
                    'is_relevant': True,
                    'goal_met': ip_type_state.get('goal_met', False),
                    'reg_nos_status': ip_type_state.get('reg_nos_status', 'Unknown')
                }

                # Include non-empty collections that are important for resuming
                for key in ['target_reg_nos', 'found_reg_nos', 'non_target_reg_nos']:
                    if key in ip_type_state and ip_type_state[key]:
                        optimized_ip_state[key] = sorted(list(ip_type_state[key]))

                # Add the optimized IP state to the main state
                optimized_state[ip_type] = optimized_ip_state

        return optimized_state

    def get_found_reg_nos(self, ip_type: str) -> Set[str]:
        """
        Returns the set of found registration numbers for the specified IP type.
        
        Args:
            ip_type: The type of IP ('trademark', 'copyright', 'patent').
            
        Returns:
            Set of registration numbers found for the specified IP type.
        """
        return self._state[ip_type]["found_reg_nos"]