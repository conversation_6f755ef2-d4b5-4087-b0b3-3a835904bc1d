import pandas as pd
import os
import json
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ
from logdata import log_message
import queue # Import queue for update_progress_queues

# Global variables for cached data
cached_cases_df = None
cached_plaintiff_df = None
cached_tro_trademarks_df = None
cached_tro_patents_df = None
cached_tro_copyrights_df = None # Keep even if unused for now
cached_plaintiff_reviews = None # For plaintiff name suggestions
cached_plaintiff_duplicates_review = None # For duplicate plaintiff review
last_refresh_time = None

# Queue for case updates (if needed centrally)
# update_progress_queues = {}
# case_processing_active = False
# case_update_queue = []

# --- Data Caching Function ---

def refresh_cached_data(force):
    """
    Loads or refreshes the global DataFrames from GZ files.
    Returns True if successful, False otherwise.
    """
    global cached_cases_df, cached_plaintiff_df, cached_tro_trademarks_df, cached_tro_patents_df, \
           cached_tro_copyrights_df, cached_plaintiff_reviews, cached_plaintiff_duplicates_review, last_refresh_time

    needs_refresh = force or cached_cases_df is None or cached_plaintiff_df is None \
                  or cached_tro_trademarks_df is None or cached_tro_patents_df is None \
                  or cached_plaintiff_reviews is None

    if not needs_refresh:
        log_message("Using cached data.", level="INFO")
        return True # Cache is considered valid

    log_message(f"Attempting to refresh cached data (force={force})...", level="INFO")
    try:
        # Load all required data tables
        cases_df = get_table_from_GZ("tb_case", force_refresh=force)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=force)
        trademark_df = get_table_from_GZ("tb_trademark", force_refresh=force)
        patent_df = get_table_from_GZ("tb_patent", force_refresh=force)
        plaintiff_reviews_df = get_table_from_GZ("tbi_case_plaintiff_name_review", force_refresh=force)
        plaintiff_duplicates_review_df = pd.DataFrame(columns=['id1', 'name1', 'cases1', 'id2', 'name2', 'cases2', 'status', 'resolution_info', 'resolution_timestamp']) # Add expected columns


        # Validate loaded data (basic check)
        if cases_df is None or plaintiff_df is None:
             log_message("Failed to load essential case or plaintiff data. Aborting refresh.", level="ERROR")
             # Keep old cache? Or set to None? Setting to None forces retry next time.
             cached_cases_df = cached_plaintiff_df = cached_tro_trademarks_df = cached_tro_patents_df = cached_plaintiff_reviews = cached_plaintiff_duplicates_review = None
             last_refresh_time = None
             return False

        # Decompress images_status if it exists
        if 'images_status' in cases_df.columns:
            cases_df['images_status'] = cases_df['images_status'].apply(
                lambda x: json.loads(x) if pd.notna(x) and isinstance(x, str) else x
            )

        # Ensure review tables are DataFrames even if empty
        if plaintiff_reviews_df is None:
            plaintiff_reviews_df = pd.DataFrame(columns=['case_id', 'proposed_name', 'method_info', 'rejected_names', 'status']) # Add expected columns

        

        # Update global variables
        cached_cases_df = cases_df
        cached_plaintiff_df = plaintiff_df
        cached_tro_trademarks_df = trademark_df
        cached_tro_patents_df = patent_df
        cached_plaintiff_reviews = plaintiff_reviews_df
        cached_plaintiff_duplicates_review = plaintiff_duplicates_review_df

        # Update last refresh time based on the case feather file modification time
        feather_file_path = os.path.join(os.getcwd(), "data", "Tables", "tb_case.feather") # Assuming this file reflects cache state
        if os.path.exists(feather_file_path):
             last_refresh_time = datetime.fromtimestamp(os.path.getmtime(feather_file_path))
        else:
             last_refresh_time = datetime.now() # Fallback if feather file doesn't exist

        log_message("Cached data refreshed successfully.", level="INFO")
        return True # Success

    except Exception as e:
        log_message(f"Error refreshing cached data: {e}", level="ERROR")
        cached_cases_df = cached_plaintiff_df = cached_tro_trademarks_df = cached_tro_patents_df = cached_plaintiff_reviews = cached_plaintiff_duplicates_review = None
        last_refresh_time = None
        return False # Indicate failure


def refresh_selected_cached_data(target_table, where_clause=None):
    """
    Loads or refreshes the global DataFrames from GZ files.
    Returns True if successful, False otherwise.
    """
    global cached_cases_df, cached_plaintiff_df, cached_tro_trademarks_df, cached_tro_patents_df, \
           cached_tro_copyrights_df, cached_plaintiff_reviews, cached_plaintiff_duplicates_review, last_refresh_time

    log_message(f"Attempting to refresh cached data (target_tables={target_table})...", level="INFO")
    try:
        # Load all required data tables
        df = get_table_from_GZ(target_table, force_refresh=True, where_clause=where_clause)

        # Decompress images_status if it exists
        if 'images_status' in df.columns:
            df['images_status'] = df['images_status'].apply(lambda x: json.loads(x) if pd.notna(x) and isinstance(x, str) else x)
            
        # Merge with existing cache if it exists
        if target_table == "tb_case" and cached_cases_df is not None:
            df = pd.concat([cached_cases_df, df], ignore_index=True).drop_duplicates(subset='id', keep='last')
            cached_cases_df = df

        log_message("Cached data for selected ids refreshed successfully.", level="INFO")
        return True # Success

    except Exception as e:
        log_message(f"Error refreshing cached data for selected ids: {e}", level="ERROR")
        return False # Indicate failure    