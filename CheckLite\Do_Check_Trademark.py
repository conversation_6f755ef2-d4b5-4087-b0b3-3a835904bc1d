from langfuse import observe
import langfuse
import ast

import shutil, time, os, asyncio
import Common.Constants as Constants
from CheckLite.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_product_url
from Check.Marks import get_all_matches, get_matches_and_check_perfect
from Check.Utils import create_ip_url



@observe(capture_input=False, capture_output=False)
async def process_single_image(client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df, precomputed_embeddings_map=None):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "query_image_path": query_image_path,
        "check_id": check_id
    })
    product_url = create_product_url(check_id, query_image_path)
    results = await process_brand_logo(client_id, query_image_path, product_url, "", check_id, temp_dir, cases_df, plaintiff_df, precomputed_embeddings_map=precomputed_embeddings_map)
    langfuse.get_client().update_current_span(output={"results": results})
    return results, []


@observe(capture_input=False, capture_output=False)
async def process_brand_logo(client_id, query_image_part_path, product_url, brand_name, check_id, temp_dir, cases_df, plaintiff_df, precomputed_embeddings_map=None):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "query_image_part_path": query_image_part_path,
        "product_url": product_url, "brand_name": brand_name, "check_id": check_id
    })
    # Logic: if the brand name is one of the TRO plaintiff, we search Qdrant only for that plaintiff. Else we search everything (with higher treshold)
    
    matches, is_perfect = get_matches_and_check_perfect(brand_name)
    match_items = matches if is_perfect else []
    applicant_names = set()
    plaintiff_ids = set()
    for match_item in match_items:
        if match_item[2][4]:
            applicant_names.add(match_item[2][4]) # 2 is metadata, 4 is applicant_name
        if match_item[2][1]:
            plaintiff_ids.add(match_item[2][1]) # 2 is metadata, 1 is plaintiff_id

    sim_results = []
    for plaintiff_id in plaintiff_ids:
        sim_results.extend(await find_similar_assets_qdrant(
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=plaintiff_id,
            top_n=1,
            similarity_threshold=0.65,
            precomputed_embeddings_map=precomputed_embeddings_map
        ))
        
    if not match_items or not sim_results:  # If we did not find using the plaintiff id, or we dont have a plaintiff id, we search amongst the logo with a higher threshold.
        # No match in plaintiff_list, so we search for the brand in trademark_texts.
        sim_results = await find_similar_assets_qdrant(
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=None,
            top_n=1,
            similarity_threshold=0.75,
            precomputed_embeddings_map=precomputed_embeddings_map
        )
    
    for sim_result in sim_results:
        sim_result["internal_type"] = "logo"

    langfuse.get_client().update_current_span(output={"results": sim_results})
    return sim_results



@observe(capture_input=False, capture_output=False)
# @profile
async def check_trademarks(client, bucket, temp_dir, client_id, check_id, local_product_images, description, ip_keywords, cases_df, plaintiff_df, precomputed_embeddings_map=None, language='zh'):
    langfuse.get_client().update_current_span(input={
        "client_id": client_id, "check_id": check_id,
        "local_product_images": local_product_images,
        "description": description, "ip_keywords": ip_keywords, "language": language
    })
    start_time = time.time()

    # Use new approach: process images with TM_AUTO search and AI analysis
    trademark_check_tasks = [
        process_single_image(
            client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df,
            precomputed_embeddings_map=precomputed_embeddings_map
        )
        for query_image_path in local_product_images
    ]
    task_returns = await asyncio.gather(*trademark_check_tasks)
    results_logo = [item for sublist in task_returns for item in sublist[0] if item is not None]
    brands = [item for sublist in task_returns for item in sublist[1] if item is not None]
    
    
    ### Collect all trademark matches from different source
    # 1. Search for matches of brand names from AI
    text_matches = []
    for brand_name in brands:
        matches, is_perfect = get_matches_and_check_perfect(brand_name)
        if is_perfect:
            text_matches.extend(matches)

    # 2. Search for exact matches of keywords
    for keyword in ip_keywords:
        matches, is_perfect = get_matches_and_check_perfect(keyword)
        if is_perfect:
            text_matches.extend(matches)

    # 3. Search for matches in description text
    text_matches.extend(get_all_matches(description))

    # 4. Reference text processing removed

    # Remove duplicates and merge text_matches
    merged_matches = {}
    for match in text_matches:
        _, _, metadata = match
        mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily = metadata
        key = (applicant_name, mark_text)

        if key not in merged_matches:
            merged_matches[key] = {
                "plaintiff_id": plaintiff_id,
                "ser_no": [ser_no],
                "reg_no": [reg_no],
                "applicant_name": applicant_name,
                "mark_text": mark_text,
                "int_cls": list(set(int_cls)) if int_cls else [],
                "goods_services_text_daily": [goods_services_text_daily] if goods_services_text_daily else [],
                "original_match": match # Keep the first match encountered
            }
        else:
            # Merge logic
            existing = merged_matches[key]
            if plaintiff_id is not None and existing["plaintiff_id"] is None:
                existing["plaintiff_id"] = plaintiff_id
            
            existing["ser_no"].append(ser_no)
            existing["reg_no"].append(reg_no)
            
            if int_cls:
                existing["int_cls"].extend(int_cls)
                existing["int_cls"] = list(set(existing["int_cls"]))
            
            if goods_services_text_daily:
                existing["goods_services_text_daily"].append(goods_services_text_daily)

    # Reconstruct the matches list from the merged data
    unique_matches = []
    for key, merged_data in merged_matches.items():
        # Recreate the metadata tuple with merged lists
        new_metadata = (merged_data["mark_text"], merged_data["plaintiff_id"],merged_data["reg_no"], merged_data["ser_no"], merged_data["applicant_name"], merged_data["int_cls"], merged_data["goods_services_text_daily"])
        # Use the start/end from the first match we saw
        original_match = merged_data["original_match"]
        unique_matches.append((original_match[0], original_match[1], new_metadata))
            
    # unique_matches sort by len("text")
    unique_matches.sort(key=lambda x: len(x[2][0]), reverse=True) # 2 is metadata, 0 is text
    filtered_unique_matches = unique_matches[:15]  # Only keep the 15 longest text (we are not going to give 100 to the AI!)

    results_text = []
    for match in unique_matches:
        _, _, metadata = match
        mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls, goods_services_text_daily = metadata
        results_text.append({
            "ip_type": "Trademark",
            "internal_type": "text",
            "reg_no": ", ".join(map(str, reg_no)) if isinstance(reg_no, list) else reg_no,
            "int_cls_list": int_cls,
            "goods_services": goods_services_text_daily,
            "text": mark_text,
            "ip_owner": applicant_name,
            "plaintiff_id": plaintiff_id,
            "ser_no": ", ".join(map(str, ser_no)) if isinstance(ser_no, list) else ser_no,
            "product_local_path": local_product_images[0] if local_product_images else "",
            "ip_asset_urls": [create_ip_url("Trademark", s.strip()) for s in ser_no] if isinstance(ser_no, list) else [create_ip_url("Trademark", ser_no.strip())],
        })

    results = results_logo + results_text
    
    # Merge results that have same ip_owner and same text:
    merged_results = []
    seen_keys = set()
    for result in results:
        key = (result["ip_owner"].lower(), result["text"])
        if key not in seen_keys:
            merged_results.append(result)
            seen_keys.add(key)
        else:
            merged_result = [merged_result for merged_result in merged_results if merged_result["ip_owner"].lower() == result["ip_owner"].lower() and merged_result["text"] == result["text"]][0]
            if result["product_local_path"] == local_product_images[0]:  # Favor the main picture
                merged_result["product_local_path"] = result["product_local_path"]  # Create report adds the product url
                
            merged_result["ip_asset_urls"].extend(result["ip_asset_urls"])
            
    print(f"📋 [CHECK:{check_id}] Logo results: {len(results_logo)}, Text results: {len(results_text)}, Merged results: {len(merged_results)}")
    
    print(f"\033[32m ✅ Trademark: Trademark Analysis DONE, for {len(local_product_images)} picture in {time.time() - start_time:.1f} seconds\033[0m")
    langfuse.get_client().update_current_span(output={"results": merged_results})
    return merged_results

if __name__ == '__main__':
    # This block allows the script to be run directly for testing purposes.

    import tempfile
    import sys

    # Ensure the project root is in the Python path to resolve imports correctly.
    sys.path.append(os.getcwd())

    from DatabaseManagement.ImportExport import get_table_from_GZ
    from CheckLite.RAG.qdrant_search import get_siglip_embeddings

    async def main():
        """Main function to run a standalone trademark check on a single local image."""
        print("--- Running Standalone Trademark Check ---")
        
        # HARDCODED: Provide a path to a local image for testing.
        test_image_path = r"D:\Win10User\Downloads\Check images\test3\product_1_dd3f3f38-35b6-4ee2-8c3e-2219adcff75b.png"
        test_image_path = r"D:\Win10User\Downloads\Check images\Image_2025-08-10_211541_013.jpg"
        local_product_images = [test_image_path]
        
        # Create a persistent directory for output, named after the test file.
        output_dir = os.path.join(os.path.dirname(test_image_path), os.path.splitext(os.path.basename(test_image_path))[0] + "_output")
        os.makedirs(output_dir, exist_ok=True)
        print(f"Using output directory: {output_dir}")

        cases_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
        
        embedding = await get_siglip_embeddings(local_product_images)
        precomputed_embeddings_map = {path: emb for path, emb in zip(local_product_images, embedding)}

        print(f"Checking file: {local_product_images[0]}")
        results = await check_trademarks(
            client=None,
            bucket=None,
            temp_dir=output_dir, # Pass the persistent output directory
            client_id="local_test",
            check_id="local_test",
            local_product_images=local_product_images,
            description="",
            ip_keywords=[],
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            precomputed_embeddings_map=precomputed_embeddings_map
        )
        print("\n--- Trademark Check Results ---")
        print(results)
        print("--- End of Report ---")

    asyncio.run(main())